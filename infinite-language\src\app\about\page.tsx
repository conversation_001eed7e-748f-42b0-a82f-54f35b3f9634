'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AboutPage() {
  const { t, language } = useLanguage();

  const getContent = () => {
    switch (language) {
      case 'vi':
        return {
          title: "Giới thiệu về Infinite English",
          subtitle: "Nền tảng học tiếng Anh AI hàng đầu dành cho người Việt",
          sections: [
            {
              title: "Về chúng tôi",
              content: "Infinite English là nền tảng học tiếng Anh trực tuyến sử dụng công nghệ AI tiên tiến, đ<PERSON><PERSON><PERSON> thiết kế đặc biệt cho người học Việt Nam. Chúng tôi cam kết mang đến trải nghiệm học tập hiệu quả, cá nhân hóa và hoàn toàn miễn phí."
            },
            {
              title: "<PERSON><PERSON> mệnh",
              content: "<PERSON>ứ mệnh của chúng tôi là làm cho việc học tiếng Anh trở nên dễ dàng, thú vị và hiệu quả cho mọi người Việt Nam. Chúng tôi tin rằng ngôn ngữ là cầu nối kết nối con người và mở ra những cơ hội mới trong cuộc sống."
            },
            {
              title: "Tầm nhìn",
              content: "Chúng tôi hướng tới việc trở thành nền tảng học tiếng Anh AI số 1 tại Việt Nam, giúp hàng triệu người Việt tự tin giao tiếp bằng tiếng Anh và thành công trong môi trường quốc tế."
            },
            {
              title: "Đặc điểm nổi bật",
              content: "• Câu hỏi trắc nghiệm vô hạn được tạo bởi AI\n• Bài học có cấu trúc theo từng cấp độ\n• Hỗ trợ đa ngôn ngữ (Việt, Anh, Trung)\n• Theo dõi tiến độ học tập chi tiết\n• Hoàn toàn miễn phí\n• Giao diện thân thiện, dễ sử dụng"
            },
            {
              title: "Công nghệ AI",
              content: "Chúng tôi sử dụng công nghệ AI tiên tiến để tạo ra nội dung học tập cá nhân hóa, phù hợp với trình độ và nhu cầu của từng người học. Hệ thống AI của chúng tôi liên tục học hỏi và cải thiện để mang đến trải nghiệm học tập tốt nhất."
            }
          ]
        };
      case 'zh':
        return {
          title: "关于 Infinite English",
          subtitle: "专为越南人设计的领先AI英语学习平台",
          sections: [
            {
              title: "关于我们",
              content: "Infinite English 是一个使用先进AI技术的在线英语学习平台，专为越南学习者设计。我们致力于提供高效、个性化且完全免费的学习体验。"
            },
            {
              title: "使命",
              content: "我们的使命是让所有越南人都能轻松、有趣且高效地学习英语。我们相信语言是连接人们的桥梁，为生活开启新的机遇。"
            },
            {
              title: "愿景",
              content: "我们致力于成为越南第一的AI英语学习平台，帮助数百万越南人自信地用英语交流，在国际环境中取得成功。"
            },
            {
              title: "突出特点",
              content: "• AI生成的无限选择题\n• 按级别结构化的课程\n• 多语言支持（越南语、英语、中文）\n• 详细的学习进度跟踪\n• 完全免费\n• 友好易用的界面"
            },
            {
              title: "AI技术",
              content: "我们使用先进的AI技术创建个性化学习内容，适合每个学习者的水平和需求。我们的AI系统不断学习和改进，以提供最佳的学习体验。"
            }
          ]
        };
      default:
        return {
          title: "About Infinite English",
          subtitle: "Leading AI English Learning Platform for Vietnamese Learners",
          sections: [
            {
              title: "About Us",
              content: "Infinite English is an online English learning platform using advanced AI technology, specifically designed for Vietnamese learners. We are committed to providing an effective, personalized, and completely free learning experience."
            },
            {
              title: "Mission",
              content: "Our mission is to make English learning easy, enjoyable, and effective for all Vietnamese people. We believe that language is a bridge connecting people and opening new opportunities in life."
            },
            {
              title: "Vision",
              content: "We aim to become the #1 AI English learning platform in Vietnam, helping millions of Vietnamese people communicate confidently in English and succeed in international environments."
            },
            {
              title: "Key Features",
              content: "• Unlimited AI-generated multiple choice questions\n• Structured lessons by level\n• Multi-language support (Vietnamese, English, Chinese)\n• Detailed learning progress tracking\n• Completely free\n• User-friendly interface"
            },
            {
              title: "AI Technology",
              content: "We use advanced AI technology to create personalized learning content that suits each learner's level and needs. Our AI system continuously learns and improves to provide the best learning experience."
            }
          ]
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {content.title}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {content.subtitle}
            </p>
          </div>

          {/* Content Sections */}
          <div className="space-y-8">
            {content.sections.map((section, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {section.title}
                </h2>
                <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {section.content}
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="mt-12 text-center">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                {language === 'vi' ? 'Bắt đầu học ngay hôm nay!' : 
                 language === 'zh' ? '今天就开始学习！' : 
                 'Start Learning Today!'}
              </h3>
              <p className="text-blue-100 mb-6">
                {language === 'vi' ? 'Tham gia cùng hàng nghìn người học khác và cải thiện tiếng Anh của bạn với AI.' :
                 language === 'zh' ? '加入成千上万的其他学习者，用AI提高您的英语水平。' :
                 'Join thousands of other learners and improve your English with AI.'}
              </p>
              <a
                href="/"
                className="inline-block bg-white text-blue-600 font-semibold px-8 py-3 rounded-xl hover:bg-blue-50 transition-colors"
              >
                {language === 'vi' ? 'Bắt đầu học' :
                 language === 'zh' ? '开始学习' :
                 'Start Learning'}
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}

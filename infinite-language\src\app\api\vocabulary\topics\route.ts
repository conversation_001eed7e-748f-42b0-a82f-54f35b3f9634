import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import VocabularyTopic from '@/models/VocabularyTopic';
import { Language, Level } from '@/types';

// GET - Get all vocabulary topics
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const language = searchParams.get('language') as Language || 'en';
    const level = searchParams.get('level') as Level;
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    // Build query
    const query: any = {
      isActive: true,
      language,
    };

    if (level) {
      query.level = level;
    }

    // Get topics with pagination
    const skip = (page - 1) * limit;
    const topics = await VocabularyTopic.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await VocabularyTopic.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: {
        topics,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching vocabulary topics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch vocabulary topics' },
      { status: 500 }
    );
  }
}

// POST - Create new vocabulary topic (admin only)
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      slug,
      description,
      icon,
      color,
      level,
      language,
      order,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body;

    // Validation
    if (!name || !slug || !description || !level) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingTopic = await VocabularyTopic.findOne({ slug });
    if (existingTopic) {
      return NextResponse.json(
        { success: false, error: 'Slug already exists' },
        { status: 400 }
      );
    }

    // Create new topic
    const topic = new VocabularyTopic({
      name,
      slug,
      description,
      icon: icon || '📚',
      color: color || '#3B82F6',
      level,
      language: language || 'en',
      order: order || 0,
      seoTitle,
      seoDescription,
      seoKeywords,
    });

    await topic.save();

    return NextResponse.json({
      success: true,
      data: topic,
    });
  } catch (error) {
    console.error('Error creating vocabulary topic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create vocabulary topic' },
      { status: 500 }
    );
  }
}

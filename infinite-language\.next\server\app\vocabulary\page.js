/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/vocabulary/page";
exports.ids = ["app/vocabulary/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvocabulary%2Fpage&page=%2Fvocabulary%2Fpage&appPaths=%2Fvocabulary%2Fpage&pagePath=private-next-app-dir%2Fvocabulary%2Fpage.tsx&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvocabulary%2Fpage&page=%2Fvocabulary%2Fpage&appPaths=%2Fvocabulary%2Fpage&pagePath=private-next-app-dir%2Fvocabulary%2Fpage.tsx&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/vocabulary/page.tsx */ \"(rsc)/./src/app/vocabulary/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'vocabulary',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\vocabulary\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\vocabulary\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/vocabulary/page\",\n        pathname: \"/vocabulary\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvocabulary%2Fpage&page=%2Fvocabulary%2Fpage&appPaths=%2Fvocabulary%2Fpage&pagePath=private-next-app-dir%2Fvocabulary%2Fpage.tsx&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(rsc)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VocabularySimple.tsx */ \"(rsc)/./src/components/VocabularySimple.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBSSU1QyU1Q3dlYm5nb25uZ3UlNUMlNUNpbmZpbml0ZS1sYW5ndWFnZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNWb2NhYnVsYXJ5U2ltcGxlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUE4SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFJXFxcXHdlYm5nb25uZ3VcXFxcaW5maW5pdGUtbGFuZ3VhZ2VcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVm9jYWJ1bGFyeVNpbXBsZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxBSVxcd2Vibmdvbm5ndVxcaW5maW5pdGUtbGFuZ3VhZ2VcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"296ed102954d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyOTZlZDEwMjk1NGRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Thông Minh Cho Người Việt\",\n        template: \"%s | Infinite English\"\n    },\n    description: \"Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. Phù hợp mọi trình độ từ cơ bản đến nâng cao. Nền tảng được hỗ trợ bởi quảng cáo để duy trì miễn phí.\",\n    keywords: [\n        \"học tiếng anh\",\n        \"học tiếng anh miễn phí\",\n        \"học tiếng anh online\",\n        \"AI học tiếng anh\",\n        \"trắc nghiệm tiếng anh\",\n        \"bài học tiếng anh\",\n        \"từ vựng tiếng anh\",\n        \"ngữ pháp tiếng anh\",\n        \"luyện nói tiếng anh\",\n        \"luyện thi TOEIC\",\n        \"luyện thi IELTS\",\n        \"phát âm tiếng anh\",\n        \"giao tiếp tiếng anh\",\n        \"khóa học tiếng anh miễn phí\",\n        \"luyện tập tiếng anh\",\n        \"tiếng anh cơ bản\",\n        \"tiếng anh giao tiếp\",\n        \"học tiếng anh cho người mới bắt đầu\",\n        \"học tiếng anh cho người Việt\",\n        \"ứng dụng học tiếng anh\",\n        \"website học tiếng anh\",\n        \"English learning\",\n        \"learn English online\",\n        \"free English course\"\n    ],\n    authors: [\n        {\n            name: \"Infinite English Team\"\n        }\n    ],\n    creator: \"Infinite English\",\n    publisher: \"Infinite English\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://inenglish.io.vn'),\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        type: 'website',\n        locale: 'vi_VN',\n        url: 'https://inenglish.io.vn',\n        title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',\n        description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',\n        siteName: 'Infinite English',\n        images: [\n            {\n                url: '/logo.png',\n                width: 1200,\n                height: 630,\n                alt: 'Infinite English - Nền Tảng Học Tiếng Anh với AI Cho Người Việt'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',\n        description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',\n        images: [\n            '/logo.png'\n        ],\n        creator: '@InfiniteEnglish'\n    },\n    alternates: {\n        canonical: '/',\n        languages: {\n            'vi-VN': '/',\n            'en-US': '/?lang=en',\n            'zh-CN': '/?lang=zh'\n        }\n    },\n    verification: {\n        google: 'your-google-verification-code',\n        yandex: 'your-yandex-verification-code',\n        yahoo: 'your-yahoo-verification-code'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"vi\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/logo.svg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#F97350\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-site-verification\",\n                        content: \"your-google-verification-code\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"inenglish.io.vn\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-adsense-account\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-adsense-platform-account\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"ads-txt-verification\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"content-language\",\n                        content: \"vi,en\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"audience\",\n                        content: \"all\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"distribution\",\n                        content: \"global\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"rating\",\n                        content: \"general\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"Infinite English\",\n                                \"description\": \"Ứng dụng học tiếng Anh miễn phí với AI tạo câu hỏi và bài tập vô hạn cho người Việt\",\n                                \"url\": \"https://inenglish.io.vn\",\n                                \"logo\": \"https://inenglish.io.vn/logo.png\",\n                                \"applicationCategory\": \"EducationalApplication\",\n                                \"operatingSystem\": \"Web Browser\",\n                                \"browserRequirements\": \"Requires JavaScript. Requires HTML5.\",\n                                \"softwareVersion\": \"1.0\",\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Infinite English Team\"\n                                },\n                                \"publisher\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Infinite English\"\n                                },\n                                \"inLanguage\": [\n                                    \"vi-VN\",\n                                    \"en-US\"\n                                ],\n                                \"audience\": {\n                                    \"@type\": \"EducationalAudience\",\n                                    \"educationalRole\": \"student\"\n                                },\n                                \"educationalUse\": \"instruction\",\n                                \"learningResourceType\": [\n                                    \"exercise\",\n                                    \"quiz\",\n                                    \"lesson\",\n                                    \"practice\"\n                                ],\n                                \"educationalLevel\": [\n                                    \"beginner\",\n                                    \"intermediate\",\n                                    \"advanced\"\n                                ],\n                                \"about\": {\n                                    \"@type\": \"Thing\",\n                                    \"name\": \"English Language Learning\",\n                                    \"description\": \"Học tiếng Anh từ cơ bản đến nâng cao\"\n                                },\n                                \"featureList\": [\n                                    \"Câu hỏi trắc nghiệm vô hạn được tạo bởi AI\",\n                                    \"Bài học có cấu trúc theo trình độ\",\n                                    \"Luyện tập từ vựng và ngữ pháp\",\n                                    \"Hỗ trợ đa ngôn ngữ\",\n                                    \"Hoàn toàn miễn phí\"\n                                ],\n                                \"isAccessibleForFree\": true,\n                                \"license\": \"https://creativecommons.org/licenses/by-nc-sa/4.0/\"\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/vocabulary/page.tsx":
/*!*************************************!*\
  !*** ./src/app/vocabulary/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VocabularyPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _components_VocabularySimple__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/VocabularySimple */ \"(rsc)/./src/components/VocabularySimple.tsx\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/VocabularyTopic */ \"(rsc)/./src/models/VocabularyTopic.ts\");\n\n\n\n\n\nconst metadata = {\n    title: 'Vocabulary Topics - Infinite English | Learn English Words by Categories',\n    description: 'Explore English vocabulary by topics with over 1000+ words. Learn new words with definitions, examples, pronunciation guides, and interactive exercises for beginner, intermediate, and advanced levels.',\n    keywords: 'English vocabulary, vocabulary topics, learn English words, vocabulary practice, English learning, word meanings, vocabulary exercises, English words by category, vocabulary building, language learning',\n    authors: [\n        {\n            name: 'Infinite English'\n        }\n    ],\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        title: 'Vocabulary Topics - Infinite English',\n        description: 'Explore English vocabulary by topics with interactive learning tools and comprehensive word lists.',\n        type: 'website',\n        locale: 'vi_VN',\n        alternateLocale: [\n            'en_US',\n            'zh_CN'\n        ],\n        siteName: 'Infinite English'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'Vocabulary Topics - Infinite English',\n        description: 'Learn English vocabulary by topics with interactive exercises.'\n    },\n    alternates: {\n        canonical: '/vocabulary',\n        languages: {\n            'vi': '/vocabulary',\n            'en': '/vocabulary',\n            'zh': '/vocabulary'\n        }\n    }\n};\nasync function getVocabularyTopics(language = 'vi') {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const topics = await _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find({\n            language,\n            isActive: true\n        }).sort({\n            order: 1,\n            createdAt: -1\n        }).limit(50).lean();\n        return topics.map((topic)=>({\n                ...topic,\n                _id: topic._id.toString(),\n                createdAt: topic.createdAt.toISOString(),\n                updatedAt: topic.updatedAt.toISOString()\n            }));\n    } catch (error) {\n        console.error('Error fetching vocabulary topics:', error);\n        return [];\n    }\n}\nasync function VocabularyPage() {\n    // Get language from headers or default to Vietnamese\n    const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const acceptLanguage = headersList.get('accept-language') || '';\n    const language = acceptLanguage.includes('zh') ? 'zh' : acceptLanguage.includes('en') ? 'en' : 'vi';\n    const topics = await getVocabularyTopics(language);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VocabularySimple__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        initialTopics: topics\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\vocabulary\\\\page.tsx\",\n        lineNumber: 81,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/vocabulary/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\webngonngu\\infinite-language\\src\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/VocabularySimple.tsx":
/*!*********************************************!*\
  !*** ./src/components/VocabularySimple.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\webngonngu\\infinite-language\\src\\components\\VocabularySimple.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n// Global is used here to maintain a cached connection across hot reloads in development\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null\n};\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/VocabularyTopic.ts":
/*!***************************************!*\
  !*** ./src/models/VocabularyTopic.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst VocabularyTopicSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    description: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    icon: {\n        type: String,\n        required: true,\n        default: '📚'\n    },\n    color: {\n        type: String,\n        required: true,\n        default: '#3B82F6'\n    },\n    level: {\n        type: String,\n        enum: [\n            'beginner',\n            'intermediate',\n            'advanced'\n        ],\n        required: true\n    },\n    language: {\n        type: String,\n        enum: [\n            'en',\n            'vi',\n            'zh'\n        ],\n        required: true,\n        default: 'en'\n    },\n    wordCount: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    order: {\n        type: Number,\n        default: 0\n    },\n    seoTitle: {\n        type: String,\n        trim: true\n    },\n    seoDescription: {\n        type: String,\n        trim: true\n    },\n    seoKeywords: [\n        {\n            type: String,\n            trim: true\n        }\n    ]\n}, {\n    timestamps: true\n});\n// Indexes for efficient queries (slug already has unique index from schema)\nVocabularyTopicSchema.index({\n    level: 1,\n    language: 1\n});\nVocabularyTopicSchema.index({\n    isActive: 1,\n    order: 1\n});\nVocabularyTopicSchema.index({\n    language: 1,\n    isActive: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).VocabularyTopic || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('VocabularyTopic', VocabularyTopicSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbW9kZWxzL1ZvY2FidWxhcnlUb3BpYy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0Q7QUFzQnRELE1BQU1FLHdCQUF3QixJQUFJRCw0Q0FBTUEsQ0FBbUI7SUFDekRFLE1BQU07UUFDSkMsTUFBTUM7UUFDTkMsVUFBVTtRQUNWQyxNQUFNO0lBQ1I7SUFDQUMsTUFBTTtRQUNKSixNQUFNQztRQUNOQyxVQUFVO1FBQ1ZHLFFBQVE7UUFDUkMsV0FBVztRQUNYSCxNQUFNO0lBQ1I7SUFDQUksYUFBYTtRQUNYUCxNQUFNQztRQUNOQyxVQUFVO1FBQ1ZDLE1BQU07SUFDUjtJQUNBSyxNQUFNO1FBQ0pSLE1BQU1DO1FBQ05DLFVBQVU7UUFDVk8sU0FBUztJQUNYO0lBQ0FDLE9BQU87UUFDTFYsTUFBTUM7UUFDTkMsVUFBVTtRQUNWTyxTQUFTO0lBQ1g7SUFDQUUsT0FBTztRQUNMWCxNQUFNQztRQUNOVyxNQUFNO1lBQUM7WUFBWTtZQUFnQjtTQUFXO1FBQzlDVixVQUFVO0lBQ1o7SUFDQVcsVUFBVTtRQUNSYixNQUFNQztRQUNOVyxNQUFNO1lBQUM7WUFBTTtZQUFNO1NBQUs7UUFDeEJWLFVBQVU7UUFDVk8sU0FBUztJQUNYO0lBQ0FLLFdBQVc7UUFDVGQsTUFBTWU7UUFDTk4sU0FBUztJQUNYO0lBQ0FPLFVBQVU7UUFDUmhCLE1BQU1pQjtRQUNOUixTQUFTO0lBQ1g7SUFDQVMsT0FBTztRQUNMbEIsTUFBTWU7UUFDTk4sU0FBUztJQUNYO0lBQ0FVLFVBQVU7UUFDUm5CLE1BQU1DO1FBQ05FLE1BQU07SUFDUjtJQUNBaUIsZ0JBQWdCO1FBQ2RwQixNQUFNQztRQUNORSxNQUFNO0lBQ1I7SUFDQWtCLGFBQWE7UUFBQztZQUNackIsTUFBTUM7WUFDTkUsTUFBTTtRQUNSO0tBQUU7QUFDSixHQUFHO0lBQ0RtQixZQUFZO0FBQ2Q7QUFFQSw0RUFBNEU7QUFDNUV4QixzQkFBc0J5QixLQUFLLENBQUM7SUFBRVosT0FBTztJQUFHRSxVQUFVO0FBQUU7QUFDcERmLHNCQUFzQnlCLEtBQUssQ0FBQztJQUFFUCxVQUFVO0lBQUdFLE9BQU87QUFBRTtBQUNwRHBCLHNCQUFzQnlCLEtBQUssQ0FBQztJQUFFVixVQUFVO0lBQUdHLFVBQVU7QUFBRTtBQUV2RCxpRUFBZXBCLHdEQUFlLENBQUM2QixlQUFlLElBQUk3QixxREFBYyxDQUFtQixtQkFBbUJFLHNCQUFzQkEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXEFJXFx3ZWJuZ29ubmd1XFxpbmZpbml0ZS1sYW5ndWFnZVxcc3JjXFxtb2RlbHNcXFZvY2FidWxhcnlUb3BpYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UsIHsgRG9jdW1lbnQsIFNjaGVtYSB9IGZyb20gJ21vbmdvb3NlJztcbmltcG9ydCB7IExhbmd1YWdlLCBMZXZlbCB9IGZyb20gJ0AvdHlwZXMnO1xuXG5leHBvcnQgaW50ZXJmYWNlIElWb2NhYnVsYXJ5VG9waWMgZXh0ZW5kcyBEb2N1bWVudCB7XG4gIF9pZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHNsdWc6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgaWNvbjogc3RyaW5nO1xuICBjb2xvcjogc3RyaW5nO1xuICBsZXZlbDogTGV2ZWw7XG4gIGxhbmd1YWdlOiBMYW5ndWFnZTtcbiAgd29yZENvdW50OiBudW1iZXI7XG4gIGlzQWN0aXZlOiBib29sZWFuO1xuICBvcmRlcjogbnVtYmVyO1xuICBzZW9UaXRsZT86IHN0cmluZztcbiAgc2VvRGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIHNlb0tleXdvcmRzPzogc3RyaW5nW107XG4gIGNyZWF0ZWRBdDogRGF0ZTtcbiAgdXBkYXRlZEF0OiBEYXRlO1xufVxuXG5jb25zdCBWb2NhYnVsYXJ5VG9waWNTY2hlbWEgPSBuZXcgU2NoZW1hPElWb2NhYnVsYXJ5VG9waWM+KHtcbiAgbmFtZToge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICB0cmltOiB0cnVlLFxuICB9LFxuICBzbHVnOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIHVuaXF1ZTogdHJ1ZSxcbiAgICBsb3dlcmNhc2U6IHRydWUsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgZGVzY3JpcHRpb246IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgaWNvbjoge1xuICAgIHR5cGU6IFN0cmluZyxcbiAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICBkZWZhdWx0OiAn8J+TmicsXG4gIH0sXG4gIGNvbG9yOiB7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGRlZmF1bHQ6ICcjM0I4MkY2JyxcbiAgfSxcbiAgbGV2ZWw6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZW51bTogWydiZWdpbm5lcicsICdpbnRlcm1lZGlhdGUnLCAnYWR2YW5jZWQnXSxcbiAgICByZXF1aXJlZDogdHJ1ZSxcbiAgfSxcbiAgbGFuZ3VhZ2U6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgZW51bTogWydlbicsICd2aScsICd6aCddLFxuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIGRlZmF1bHQ6ICdlbicsXG4gIH0sXG4gIHdvcmRDb3VudDoge1xuICAgIHR5cGU6IE51bWJlcixcbiAgICBkZWZhdWx0OiAwLFxuICB9LFxuICBpc0FjdGl2ZToge1xuICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgZGVmYXVsdDogdHJ1ZSxcbiAgfSxcbiAgb3JkZXI6IHtcbiAgICB0eXBlOiBOdW1iZXIsXG4gICAgZGVmYXVsdDogMCxcbiAgfSxcbiAgc2VvVGl0bGU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgc2VvRGVzY3JpcHRpb246IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgdHJpbTogdHJ1ZSxcbiAgfSxcbiAgc2VvS2V5d29yZHM6IFt7XG4gICAgdHlwZTogU3RyaW5nLFxuICAgIHRyaW06IHRydWUsXG4gIH1dLFxufSwge1xuICB0aW1lc3RhbXBzOiB0cnVlLFxufSk7XG5cbi8vIEluZGV4ZXMgZm9yIGVmZmljaWVudCBxdWVyaWVzIChzbHVnIGFscmVhZHkgaGFzIHVuaXF1ZSBpbmRleCBmcm9tIHNjaGVtYSlcblZvY2FidWxhcnlUb3BpY1NjaGVtYS5pbmRleCh7IGxldmVsOiAxLCBsYW5ndWFnZTogMSB9KTtcblZvY2FidWxhcnlUb3BpY1NjaGVtYS5pbmRleCh7IGlzQWN0aXZlOiAxLCBvcmRlcjogMSB9KTtcblZvY2FidWxhcnlUb3BpY1NjaGVtYS5pbmRleCh7IGxhbmd1YWdlOiAxLCBpc0FjdGl2ZTogMSB9KTtcblxuZXhwb3J0IGRlZmF1bHQgbW9uZ29vc2UubW9kZWxzLlZvY2FidWxhcnlUb3BpYyB8fCBtb25nb29zZS5tb2RlbDxJVm9jYWJ1bGFyeVRvcGljPignVm9jYWJ1bGFyeVRvcGljJywgVm9jYWJ1bGFyeVRvcGljU2NoZW1hKTtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIlNjaGVtYSIsIlZvY2FidWxhcnlUb3BpY1NjaGVtYSIsIm5hbWUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJ0cmltIiwic2x1ZyIsInVuaXF1ZSIsImxvd2VyY2FzZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImRlZmF1bHQiLCJjb2xvciIsImxldmVsIiwiZW51bSIsImxhbmd1YWdlIiwid29yZENvdW50IiwiTnVtYmVyIiwiaXNBY3RpdmUiLCJCb29sZWFuIiwib3JkZXIiLCJzZW9UaXRsZSIsInNlb0Rlc2NyaXB0aW9uIiwic2VvS2V5d29yZHMiLCJ0aW1lc3RhbXBzIiwiaW5kZXgiLCJtb2RlbHMiLCJWb2NhYnVsYXJ5VG9waWMiLCJtb2RlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/models/VocabularyTopic.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/VocabularySimple.tsx */ \"(ssr)/./src/components/VocabularySimple.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNBSSU1QyU1Q3dlYm5nb25uZ3UlNUMlNUNpbmZpbml0ZS1sYW5ndWFnZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNWb2NhYnVsYXJ5U2ltcGxlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUE4SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEFJXFxcXHdlYm5nb25uZ3VcXFxcaW5maW5pdGUtbGFuZ3VhZ2VcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVm9jYWJ1bGFyeVNpbXBsZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CVocabularySimple.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/PageTransition.tsx":
/*!*******************************************!*\
  !*** ./src/components/PageTransition.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageTransition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction PageTransition({ children }) {\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetUrl, setTargetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const previousPathnameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(pathname);\n    // Reset transition state when pathname changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            if (previousPathnameRef.current !== pathname) {\n                setIsTransitioning(false);\n                setTargetUrl(null);\n                if (timeoutRef.current) {\n                    clearTimeout(timeoutRef.current);\n                    timeoutRef.current = null;\n                }\n                previousPathnameRef.current = pathname;\n            }\n        }\n    }[\"PageTransition.useEffect\"], [\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            const handleRouteChange = {\n                \"PageTransition.useEffect.handleRouteChange\": ()=>{\n                    setIsTransitioning(false);\n                    setTargetUrl(null);\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                        timeoutRef.current = null;\n                    }\n                }\n            }[\"PageTransition.useEffect.handleRouteChange\"];\n            // Listen for route changes\n            window.addEventListener('popstate', handleRouteChange);\n            return ({\n                \"PageTransition.useEffect\": ()=>{\n                    window.removeEventListener('popstate', handleRouteChange);\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"PageTransition.useEffect\"];\n        }\n    }[\"PageTransition.useEffect\"], []);\n    // Override link clicks to add transition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            const handleLinkClick = {\n                \"PageTransition.useEffect.handleLinkClick\": (e)=>{\n                    const target = e.target;\n                    const link = target.closest('a[href]');\n                    if (link && link.href && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {\n                        const url = new URL(link.href);\n                        const currentUrl = new URL(window.location.href);\n                        // Only intercept internal links and avoid same-page navigation\n                        if (url.origin === currentUrl.origin && !link.target && url.pathname + url.search !== currentUrl.pathname + currentUrl.search) {\n                            e.preventDefault();\n                            setTargetUrl(url.pathname + url.search);\n                            setIsTransitioning(true);\n                            // Clear any existing timeout\n                            if (timeoutRef.current) {\n                                clearTimeout(timeoutRef.current);\n                            }\n                            // Navigate after a short delay to show transition\n                            timeoutRef.current = setTimeout({\n                                \"PageTransition.useEffect.handleLinkClick\": ()=>{\n                                    router.push(url.pathname + url.search);\n                                    // Fallback: reset transition state after 5 seconds if navigation doesn't complete\n                                    timeoutRef.current = setTimeout({\n                                        \"PageTransition.useEffect.handleLinkClick\": ()=>{\n                                            setIsTransitioning(false);\n                                            setTargetUrl(null);\n                                        }\n                                    }[\"PageTransition.useEffect.handleLinkClick\"], 5000);\n                                }\n                            }[\"PageTransition.useEffect.handleLinkClick\"], 300);\n                        }\n                    }\n                }\n            }[\"PageTransition.useEffect.handleLinkClick\"];\n            document.addEventListener('click', handleLinkClick);\n            return ({\n                \"PageTransition.useEffect\": ()=>{\n                    document.removeEventListener('click', handleLinkClick);\n                }\n            })[\"PageTransition.useEffect\"];\n        }\n    }[\"PageTransition.useEffect\"], [\n        router\n    ]);\n    if (isTransitioning) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-20 w-20 border-4 border-blue-200 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-20 w-20 border-4 border-blue-500 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white text-lg\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl px-8 py-4 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-800 text-xl font-semibold mb-2\",\n                                    children: t('navigating') || 'Navigating...'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: targetUrl && `Going to ${targetUrl}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-indigo-500 rounded-full animate-bounce delay-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-200\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"transition-opacity duration-300 ease-in-out\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PageTransition.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_PageTransition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PageTransition */ \"(ssr)/./src/components/PageTransition.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Providers({ children }) {\n    // Allow disabling page transitions via environment variable or for debugging\n    const enableTransitions = process.env.NEXT_PUBLIC_ENABLE_PAGE_TRANSITIONS !== 'false';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.LanguageProvider, {\n                children: enableTransitions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageTransition__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 13\n                }, this) : children\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VocabularySimple.tsx":
/*!*********************************************!*\
  !*** ./src/components/VocabularySimple.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VocabularySimple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction VocabularySimple({ initialTopics }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [topics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTopics);\n    const [selectedLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Handle level selection by navigating to appropriate URL\n    const handleLevelChange = (level)=>{\n        if (level === 'all') {\n            router.push('/vocabulary');\n        } else {\n            router.push(`/vocabulary/${level}`);\n        }\n    };\n    const handleTopicClick = (slug)=>{\n        router.push(`/vocabulary/${slug}`);\n    };\n    const getLevelText = (level)=>{\n        switch(level){\n            case 'beginner':\n                return 'Cơ bản';\n            case 'intermediate':\n                return 'Trung cấp';\n            case 'advanced':\n                return 'Nâng cao';\n            default:\n                return level;\n        }\n    };\n    const getLevelDisplayText = (level)=>{\n        if (level === 'all') {\n            return 'Tất cả';\n        }\n        return getLevelText(level);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-24 pb-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n                                children: \"Từ Vựng Theo Chủ Đề\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Kh\\xe1m ph\\xe1 từ vựng tiếng Anh theo c\\xe1c chủ đề kh\\xe1c nhau. Học từ mới với định nghĩa, v\\xed dụ v\\xe0 hướng dẫn ph\\xe1t \\xe2m.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-2 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    'all',\n                                    'beginner',\n                                    'intermediate',\n                                    'advanced'\n                                ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLevelChange(level),\n                                        className: `px-4 py-2 rounded-lg font-medium transition-all duration-200 ${selectedLevel === level ? 'bg-blue-500 text-white shadow-md' : 'text-gray-600 hover:bg-gray-100'}`,\n                                        children: getLevelDisplayText(level)\n                                    }, level, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    topics.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                        children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>handleTopicClick(topic.slug),\n                                className: \"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4\",\n                                                style: {\n                                                    backgroundColor: `${topic.color}20`,\n                                                    color: topic.color\n                                                },\n                                                children: topic.icon\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-lg text-gray-900 mb-1\",\n                                                        children: topic.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `text-xs px-2 py-1 rounded-full ${topic.level === 'beginner' ? 'bg-green-100 text-green-700' : topic.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`,\n                                                                children: getLevelText(topic.level)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    topic.wordCount,\n                                                                    \" từ\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm line-clamp-2\",\n                                        children: topic.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, topic._id, true, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCDA\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-600 mb-2\",\n                                children: \"Kh\\xf4ng t\\xecm thấy chủ đề n\\xe0o\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Thử thay đổi bộ lọc hoặc quay lại sau.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\VocabularySimple.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VocabularySimple.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loading = status === 'loading';\n    const checkAuth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[checkAuth]\": async ()=>{\n            const customSession = session;\n            if (customSession?.user) {\n                try {\n                    const response = await fetch('/api/auth/me');\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    } else {\n                        // If API fails, create user object from session\n                        setUser({\n                            id: customSession.user.id || '',\n                            email: customSession.user.email || '',\n                            username: customSession.user.username || customSession.user.name || '',\n                            preferredLanguage: 'en',\n                            stats: {\n                                totalQuestions: 0,\n                                correctAnswers: 0,\n                                streakCount: 0,\n                                lastActiveDate: new Date(),\n                                levelStats: {\n                                    beginner: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    intermediate: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    advanced: {\n                                        total: 0,\n                                        correct: 0\n                                    }\n                                }\n                            }\n                        });\n                    }\n                } catch (error) {\n                    console.error('Auth check error:', error);\n                    // Fallback to session data\n                    setUser({\n                        id: customSession.user.id || '',\n                        email: customSession.user.email || '',\n                        username: customSession.user.username || customSession.user.name || '',\n                        preferredLanguage: 'en',\n                        stats: {\n                            totalQuestions: 0,\n                            correctAnswers: 0,\n                            streakCount: 0,\n                            lastActiveDate: new Date(),\n                            levelStats: {\n                                beginner: {\n                                    total: 0,\n                                    correct: 0\n                                },\n                                intermediate: {\n                                    total: 0,\n                                    correct: 0\n                                },\n                                advanced: {\n                                    total: 0,\n                                    correct: 0\n                                }\n                            }\n                        }\n                    });\n                }\n            } else {\n                setUser(null);\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuth]\"], [\n        session\n    ]);\n    const logout = async ()=>{\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n                redirect: false\n            });\n            setUser(null);\n        } catch (error) {\n            console.error('Logout error:', error);\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuth\n    ]);\n    const value = {\n        user,\n        logout,\n        loading,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _i18n_translations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/translations */ \"(ssr)/./src/i18n/translations.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('vi');\n    // Load language from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('language');\n            if (savedLanguage && [\n                'en',\n                'vi',\n                'zh'\n            ].includes(savedLanguage)) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Save language to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            localStorage.setItem('language', language);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    const t = (key)=>{\n        const keys = key.split('.');\n        let value = _i18n_translations__WEBPACK_IMPORTED_MODULE_2__.translations[language];\n        for (const k of keys){\n            if (value && typeof value === 'object' && k in value) {\n                value = value[k];\n            } else {\n                return key;\n            }\n        }\n        return typeof value === 'string' ? value : key;\n    };\n    const value = {\n        language,\n        setLanguage,\n        t\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/i18n/translations.ts":
/*!**********************************!*\
  !*** ./src/i18n/translations.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    en: {\n        //Login\n        welcomeMessage: \"Sign in to track your progress and continue learning\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Learn English with AI-powered lessons and quizzes\",\n        // Level Selection\n        beginner: \"Beginner\",\n        intermediate: \"Intermediate\",\n        advanced: \"Advanced\",\n        beginnerDesc: \"Basic vocabulary and simple grammar\",\n        intermediateDesc: \"More complex sentences and vocabulary\",\n        advancedDesc: \"Complex grammar and advanced vocabulary\",\n        // Navigation\n        backToLevels: \"← Back to Levels\",\n        nextQuestion: \"Next Question\",\n        // Quiz\n        score: \"Score\",\n        correct: \"Correct!\",\n        incorrect: \"Incorrect!\",\n        question: \"Question:\",\n        yourAnswer: \"Your answer:\",\n        correctAnswer: \"Correct answer:\",\n        explanation: \"Explanation:\",\n        answers: \"Answers\",\n        total: \"Total\",\n        selectAnswer: \"Select your answer\",\n        answerSelected: \"Answer selected! Checking...\",\n        // Loading\n        generatingQuestion: \"Generating your question...\",\n        creatingQuestion: \"Creating a personalized question just for you\",\n        navigating: \"Navigating...\",\n        loading: \"Loading...\",\n        // Score\n        resetScore: \"Reset Score\",\n        // Language Switcher\n        language: \"Language\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Why Choose Infinite English?\",\n        whyChooseSubtitle: \"Experience the future of language learning with our cutting-edge features\",\n        aiGeneratedTitle: \"Unlimited Questions\",\n        aiGeneratedDesc: \"Endless unique questions with personalized learning experience tailored to your level\",\n        multiLanguageTitle: \"Multi-Language Support\",\n        multiLanguageDesc: \"Learn in Vietnamese, Chinese, or English with seamless language switching\",\n        progressTrackingTitle: \"Progress Tracking\",\n        progressTrackingDesc: \"Monitor your learning journey with detailed statistics and performance analytics\",\n        // Stats\n        questionsLabel: \"Questions\",\n        languagesLabel: \"Languages\",\n        aiPoweredLabel: \"Smart Learning\",\n        // Authentication\n        login: \"Login\",\n        register: \"Register\",\n        logout: \"Logout\",\n        email: \"Email\",\n        username: \"Username\",\n        password: \"Password\",\n        confirmPassword: \"Confirm Password\",\n        needAccount: \"Don't have an account? Register\",\n        haveAccount: \"Already have an account? Login\",\n        loginRequired: \"Login Required\",\n        loginToViewHistory: \"Please login to view your quiz history\",\n        // User Stats\n        stats: \"Statistics\",\n        totalQuestions: \"Total Questions\",\n        accuracy: \"Accuracy\",\n        streak: \"Streak\",\n        viewHistory: \"View History\",\n        // History\n        quizHistory: \"Quiz History\",\n        backToQuiz: \"Back to Quiz\",\n        allLevels: \"All Levels\",\n        allResults: \"All Results\",\n        result: \"Result\",\n        noHistory: \"No quiz history found\",\n        previous: \"Previous\",\n        next: \"Next\",\n        // AI Features\n        aiLessons: \"AI Lessons\",\n        aiReading: \"AI Reading\",\n        aiTutor: \"AI Tutor\",\n        createLesson: \"Create Lesson\",\n        generateReading: \"Generate Reading\",\n        askTutor: \"Ask AI Tutor\",\n        // Lessons\n        lessonTitle: \"AI English Lessons\",\n        lessonSubtitle: \"Create personalized English lessons with AI. Choose your topic, level, and lesson type to generate comprehensive learning content.\",\n        lessonType: \"Lesson Type\",\n        duration: \"Duration\",\n        topic: \"Topic\",\n        topicPlaceholder: \"e.g., Present Simple Tense, Business English, Travel Vocabulary...\",\n        topicSuggestions: \"Or choose from popular topics:\",\n        generateLesson: \"Generate Lesson\",\n        generating: \"Generating...\",\n        // Reading\n        readingTitle: \"AI Reading Practice\",\n        readingSubtitle: \"Generate personalized reading passages with AI. Choose your level, topic, and style to create unlimited practice content.\",\n        wordCount: \"Word Count\",\n        style: \"Style\",\n        generatePassage: \"Generate Passage\",\n        vocabulary: \"Vocabulary\",\n        comprehensionQuestions: \"Comprehension Questions\",\n        submitAnswers: \"Submit Answers\",\n        tryAgain: \"Try Again\",\n        generateNew: \"Generate New Passage\",\n        // Dashboard\n        dashboardTitle: \"Learning Dashboard\",\n        dashboardSubtitle: \"Track your progress, analyze your performance, and get personalized recommendations to improve your English.\",\n        quizzesTaken: \"Quizzes Taken\",\n        overallPerformance: \"Overall Performance\",\n        streakDays: \"Day Streak\",\n        studyTime: \"Study Time\",\n        progressByLevel: \"Progress by Level\",\n        aiAnalysis: \"AI Performance Analysis\",\n        strengths: \"Strengths\",\n        improvements: \"Areas for Improvement\",\n        nextSteps: \"Next Steps\",\n        motivationalMessage: \"Motivational Message\",\n        continuelearning: \"Continue Learning\",\n        // Tutor\n        tutorTitle: \"AI English Tutor\",\n        tutorSubtitle: \"Get instant help with grammar, vocabulary, pronunciation, and more. Ask questions in your native language.\",\n        askQuestion: \"Ask a question...\",\n        send: \"Send\",\n        grammarHelp: \"Grammar Help\",\n        vocabularyHelp: \"Vocabulary\",\n        pronunciationHelp: \"Pronunciation\",\n        writingHelp: \"Writing Help\",\n        conversationHelp: \"Conversation\",\n        cultureHelp: \"Culture\",\n        // Footer\n        footerDescription: \"Learn English for free with our AI-powered platform designed for Vietnamese learners. Unlimited multiple choice questions, structured lessons, vocabulary and effective learning tips. Suitable for all levels from basic to advanced.\",\n        contactAdvertising: \"Contact for Advertising\",\n        quickLinks: \"Quick Links\",\n        about: \"About\",\n        contact: \"Contact\",\n        legal: \"Legal\",\n        terms: \"Terms of Service\",\n        privacy: \"Privacy Policy\",\n        allRightsReserved: \"All rights reserved.\",\n        disclaimer: \"Disclaimer\",\n        disclaimerText: \"Our website only provides online English learning services for entertainment and content sharing purposes. All learning content posted on the website is collected from various sources on the internet and we are not responsible for copyright or ownership of any content. If you are a copyright owner and believe that content on the site violates your rights, please contact us to remove the infringing content promptly. In addition, we are not responsible for advertising content displayed on the website, including but not limited to advertising products or services of third parties. These advertisements do not reflect our views or commitments. Users need to consider and take responsibility when interacting with such advertisements.\",\n        // Common\n        level: \"Level\",\n        back: \"Back\",\n        continue: \"Continue\",\n        complete: \"Complete\",\n        start: \"Start\",\n        finish: \"Finish\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        close: \"Close\",\n        signIn: \"Sign In\",\n        signOut: \"Sign Out\",\n        signUp: \"Sign Up\",\n        // Auth Modal\n        continueWithGoogle: \"Continue with Google\",\n        continueWithFacebook: \"Continue with Facebook\",\n        optionalLoginNote: \"Login is optional. You can continue without an account, but your progress won't be saved.\",\n        loginError: \"Failed to login with {provider}. Please try again.\",\n        // Language Switcher\n        active: \"Active\",\n        // User Menu\n        learningDashboard: \"Learning Dashboard\",\n        accuracyText: \"accuracy\",\n        // Error Messages\n        aiErrorMessage: \"Sorry, I encountered an error while processing your question. Please try again.\",\n        failedToGenerate: \"Failed to Generate Question\",\n        questionGenerateError: \"Sorry, we couldn't generate a question at this time. Please try again later.\",\n        tryAgainLater: \"Try Again Later\",\n        backToHome: \"Back to Home\",\n        // Loading States\n        processing: \"Processing...\",\n        pleaseWait: \"Please wait...\",\n        loadingHistory: \"Loading your quiz history...\",\n        // General Error States\n        somethingWentWrong: \"Something went wrong\",\n        errorOccurred: \"An error occurred\",\n        retryAction: \"Retry\",\n        // Additional Reading Component\n        aiGenerates: \"AI Generates\",\n        aiGeneratesDesc: \"Our AI creates a unique passage with questions\",\n        failedToGeneratePassage: \"Failed to generate reading passage. Please try again.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Creates\",\n        aiCreatesDesc: \"AI generates comprehensive lesson content\",\n        failedToGenerateLesson: \"Failed to generate lesson. Please try again.\",\n        // History Page\n        noHistoryYet: \"No History Yet\",\n        // Question Card\n        processingAnswer: \"Processing your answer...\",\n        chooseAnswer: \"Choose your answer\"\n    },\n    vi: {\n        //Login\n        welcomeMessage: \"Đăng nhập để theo dõi tiến độ và tiếp tục học\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Học tiếng Anh với bài học và quiz AI\",\n        // Level Selection\n        beginner: \"Cơ bản\",\n        intermediate: \"Trung bình\",\n        advanced: \"Nâng cao\",\n        beginnerDesc: \"Từ vựng cơ bản và ngữ pháp đơn giản\",\n        intermediateDesc: \"Câu và từ vựng phức tạp hơn\",\n        advancedDesc: \"Ngữ pháp phức tạp và từ vựng nâng cao\",\n        // Navigation\n        backToLevels: \"Quay lại Cấp độ\",\n        nextQuestion: \"Câu hỏi tiếp theo\",\n        // Quiz\n        score: \"Điểm\",\n        correct: \"Đúng rồi!\",\n        incorrect: \"Sai rồi!\",\n        question: \"Câu hỏi:\",\n        yourAnswer: \"Câu trả lời của bạn:\",\n        correctAnswer: \"Đáp án đúng:\",\n        explanation: \"Giải thích:\",\n        answers: \"Câu trả lời\",\n        total: \"Tổng\",\n        selectAnswer: \"Chọn câu trả lời của bạn\",\n        answerSelected: \"Đã chọn câu trả lời! Đang kiểm tra...\",\n        // Loading\n        generatingQuestion: \"Đang tạo câu hỏi cho bạn...\",\n        creatingQuestion: \"Đang tạo câu hỏi cá nhân hóa dành riêng cho bạn\",\n        navigating: \"Đang chuyển trang...\",\n        loading: \"Đang tải...\",\n        // Score\n        resetScore: \"Đặt lại điểm\",\n        // Language Switcher\n        language: \"Ngôn ngữ\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Tại sao chọn Infinite English?\",\n        whyChooseSubtitle: \"Trải nghiệm tương lai của việc học ngôn ngữ với các tính năng tiên tiến\",\n        aiGeneratedTitle: \"Câu hỏi vô hạn\",\n        aiGeneratedDesc: \"Câu hỏi độc đáo không giới hạn với trải nghiệm học tập cá nhân hóa phù hợp với trình độ của bạn\",\n        multiLanguageTitle: \"Hỗ trợ đa ngôn ngữ\",\n        multiLanguageDesc: \"Học bằng tiếng Việt, tiếng Trung hoặc tiếng Anh với khả năng chuyển đổi ngôn ngữ liền mạch\",\n        progressTrackingTitle: \"Theo dõi tiến độ\",\n        progressTrackingDesc: \"Theo dõi hành trình học tập của bạn với thống kê chi tiết và phân tích hiệu suất\",\n        // Stats\n        questionsLabel: \"Câu hỏi\",\n        languagesLabel: \"Ngôn ngữ\",\n        aiPoweredLabel: \"Học thông minh\",\n        // Authentication\n        login: \"Đăng nhập\",\n        register: \"Đăng ký\",\n        logout: \"Đăng xuất\",\n        email: \"Email\",\n        username: \"Tên người dùng\",\n        password: \"Mật khẩu\",\n        confirmPassword: \"Xác nhận mật khẩu\",\n        needAccount: \"Chưa có tài khoản? Đăng ký\",\n        haveAccount: \"Đã có tài khoản? Đăng nhập\",\n        loginRequired: \"Yêu cầu đăng nhập\",\n        loginToViewHistory: \"Vui lòng đăng nhập để xem lịch sử quiz\",\n        // User Stats\n        stats: \"Thống kê\",\n        totalQuestions: \"Tổng câu hỏi\",\n        accuracy: \"Độ chính xác\",\n        streak: \"Chuỗi đúng\",\n        viewHistory: \"Xem lịch sử\",\n        // History\n        quizHistory: \"Lịch sử Quiz\",\n        backToQuiz: \"Quay lại Quiz\",\n        allLevels: \"Tất cả cấp độ\",\n        allResults: \"Tất cả kết quả\",\n        result: \"Kết quả\",\n        noHistory: \"Không tìm thấy lịch sử quiz\",\n        previous: \"Trước\",\n        next: \"Tiếp\",\n        // AI Features\n        aiLessons: \"Bài học AI\",\n        aiReading: \"Đọc hiểu AI\",\n        aiTutor: \"Gia sư AI\",\n        createLesson: \"Tạo bài học\",\n        generateReading: \"Tạo bài đọc\",\n        askTutor: \"Hỏi gia sư AI\",\n        // Lessons\n        lessonTitle: \"Bài học tiếng Anh AI\",\n        lessonSubtitle: \"Tạo bài học tiếng Anh cá nhân hóa với AI. Chọn chủ đề, cấp độ và loại bài học để tạo nội dung học tập toàn diện.\",\n        lessonType: \"Loại bài học\",\n        duration: \"Thời lượng\",\n        topic: \"Chủ đề\",\n        topicPlaceholder: \"ví dụ: Thì hiện tại đơn, Tiếng Anh thương mại, Từ vựng du lịch...\",\n        topicSuggestions: \"Hoặc chọn từ các chủ đề phổ biến:\",\n        generateLesson: \"Tạo bài học\",\n        generating: \"Đang tạo...\",\n        // Reading\n        readingTitle: \"Luyện đọc hiểu AI\",\n        readingSubtitle: \"Tạo đoạn văn đọc hiểu cá nhân hóa với AI. Chọn cấp độ, chủ đề và phong cách để tạo nội dung luyện tập không giới hạn.\",\n        wordCount: \"Số từ\",\n        style: \"Phong cách\",\n        generatePassage: \"Tạo đoạn văn\",\n        vocabulary: \"Từ vựng\",\n        comprehensionQuestions: \"Câu hỏi đọc hiểu\",\n        submitAnswers: \"Nộp bài\",\n        tryAgain: \"Thử lại\",\n        generateNew: \"Tạo đoạn văn mới\",\n        // Dashboard\n        dashboardTitle: \"Bảng điều khiển học tập\",\n        dashboardSubtitle: \"Theo dõi tiến độ, phân tích hiệu suất và nhận gợi ý cá nhân hóa để cải thiện tiếng Anh.\",\n        quizzesTaken: \"Bài quiz đã làm\",\n        overallPerformance: \"Hiệu suất tổng thể\",\n        streakDays: \"Chuỗi ngày\",\n        studyTime: \"Thời gian học\",\n        progressByLevel: \"Tiến độ theo cấp độ\",\n        aiAnalysis: \"Phân tích hiệu suất AI\",\n        strengths: \"Điểm mạnh\",\n        improvements: \"Cần cải thiện\",\n        nextSteps: \"Bước tiếp theo\",\n        motivationalMessage: \"Thông điệp động viên\",\n        continuelearning: \"Tiếp tục học\",\n        // Tutor\n        tutorTitle: \"Gia sư tiếng Anh AI\",\n        tutorSubtitle: \"Nhận trợ giúp tức thì về ngữ pháp, từ vựng, phát âm và nhiều hơn nữa. Đặt câu hỏi bằng tiếng mẹ đẻ.\",\n        askQuestion: \"Đặt câu hỏi...\",\n        send: \"Gửi\",\n        grammarHelp: \"Trợ giúp ngữ pháp\",\n        vocabularyHelp: \"Từ vựng\",\n        pronunciationHelp: \"Phát âm\",\n        writingHelp: \"Trợ giúp viết\",\n        conversationHelp: \"Hội thoại\",\n        cultureHelp: \"Văn hóa\",\n        // Footer\n        footerDescription: \"Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. Phù hợp mọi trình độ từ cơ bản đến nâng cao.\",\n        contactAdvertising: \"Liên hệ đặt quảng cáo\",\n        quickLinks: \"Liên kết nhanh\",\n        about: \"Giới thiệu\",\n        contact: \"Liên hệ\",\n        legal: \"Pháp lý\",\n        terms: \"Điều khoản\",\n        privacy: \"Chính sách bảo mật\",\n        allRightsReserved: \"Tất cả quyền được bảo lưu.\",\n        disclaimer: \"Miễn trừ trách nhiệm\",\n        disclaimerText: \"Trang web của chúng tôi chỉ cung cấp dịch vụ học tiếng Anh online với mục đích giải trí và chia sẻ nội dung. Toàn bộ nội dung học tập được đăng tải trên trang web được sưu tầm từ nhiều nguồn trên internet và chúng tôi không chịu trách nhiệm về bản quyền hoặc quyền sở hữu đối với bất kỳ nội dung nào. Nếu bạn là chủ sở hữu bản quyền và cho rằng nội dung trên trang vi phạm quyền của bạn, vui lòng liên hệ với chúng tôi để tiến hành gỡ bỏ nội dung vi phạm một cách kịp thời. Ngoài ra, chúng tôi không chịu trách nhiệm về các nội dung quảng cáo hiển thị trên trang web, bao gồm nhưng không giới hạn ở việc quảng cáo sản phẩm hoặc dịch vụ của bên thứ ba. Những quảng cáo này không phản ánh quan điểm hoặc cam kết của chúng tôi. Người dùng cần tự cân nhắc và chịu trách nhiệm khi tương tác với các quảng cáo đó.\",\n        // Common\n        level: \"Cấp độ\",\n        back: \"Quay lại\",\n        continue: \"Tiếp tục\",\n        complete: \"Hoàn thành\",\n        start: \"Bắt đầu\",\n        finish: \"Kết thúc\",\n        save: \"Lưu\",\n        cancel: \"Hủy\",\n        close: \"Đóng\",\n        signIn: \"Đăng nhập\",\n        signOut: \"Đăng xuất\",\n        signUp: \"Đăng ký\",\n        // Auth Modal\n        continueWithGoogle: \"Tiếp tục với Google\",\n        continueWithFacebook: \"Tiếp tục với Facebook\",\n        optionalLoginNote: \"Đăng nhập là tùy chọn. Bạn có thể tiếp tục mà không cần tài khoản, nhưng tiến độ sẽ không được lưu.\",\n        loginError: \"Đăng nhập với {provider} thất bại. Vui lòng thử lại.\",\n        // Language Switcher\n        active: \"Đang hoạt động\",\n        // User Menu\n        learningDashboard: \"Bảng điều khiển học tập\",\n        accuracyText: \"độ chính xác\",\n        // Error Messages\n        aiErrorMessage: \"Xin lỗi, tôi gặp lỗi khi xử lý câu hỏi của bạn. Vui lòng thử lại.\",\n        failedToGenerate: \"Không thể tạo câu hỏi\",\n        questionGenerateError: \"Xin lỗi, chúng tôi không thể tạo câu hỏi vào lúc này. Vui lòng thử lại sau.\",\n        tryAgainLater: \"Thử lại sau\",\n        backToHome: \"Về trang chủ\",\n        // Loading States\n        processing: \"Đang xử lý...\",\n        pleaseWait: \"Vui lòng đợi...\",\n        loadingHistory: \"Đang tải lịch sử quiz của bạn...\",\n        // General Error States\n        somethingWentWrong: \"Đã xảy ra lỗi\",\n        errorOccurred: \"Đã xảy ra lỗi\",\n        retryAction: \"Thử lại\",\n        // Additional Reading Component\n        aiGenerates: \"AI Tạo nội dung\",\n        aiGeneratesDesc: \"AI của chúng tôi tạo ra đoạn văn độc đáo với các câu hỏi\",\n        failedToGeneratePassage: \"Không thể tạo đoạn văn đọc hiểu. Vui lòng thử lại.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Tạo bài học\",\n        aiCreatesDesc: \"AI tạo ra nội dung bài học toàn diện\",\n        failedToGenerateLesson: \"Không thể tạo bài học. Vui lòng thử lại.\",\n        // History Page\n        noHistoryYet: \"Chưa có lịch sử\",\n        // Question Card\n        processingAnswer: \"Đang xử lý câu trả lời của bạn...\",\n        chooseAnswer: \"Chọn câu trả lời của bạn\"\n    },\n    zh: {\n        //Login\n        welcomeMessage: \"登录以跟踪您的进度并继续学习\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"用AI课程和测验学英语\",\n        // Level Selection\n        beginner: \"初级\",\n        intermediate: \"中级\",\n        advanced: \"高级\",\n        beginnerDesc: \"基础词汇和简单语法\",\n        intermediateDesc: \"更复杂的句子和词汇\",\n        advancedDesc: \"复杂语法和高级词汇\",\n        // Navigation\n        backToLevels: \"← 返回级别\",\n        nextQuestion: \"下一题\",\n        // Quiz\n        score: \"分数\",\n        correct: \"正确！\",\n        incorrect: \"错误！\",\n        question: \"问题：\",\n        yourAnswer: \"您的答案：\",\n        correctAnswer: \"正确答案：\",\n        explanation: \"解释：\",\n        answers: \"答案\",\n        total: \"总计\",\n        selectAnswer: \"选择您的答案\",\n        answerSelected: \"已选择答案！正在检查...\",\n        // Loading\n        generatingQuestion: \"正在为您生成问题...\",\n        creatingQuestion: \"正在为您创建个性化问题\",\n        navigating: \"正在导航...\",\n        loading: \"加载中...\",\n        // Score\n        resetScore: \"重置分数\",\n        // Language Switcher\n        language: \"语言\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"为什么选择 Infinite English？\",\n        whyChooseSubtitle: \"体验我们尖端功能带来的语言学习未来\",\n        aiGeneratedTitle: \"无限题库\",\n        aiGeneratedDesc: \"无限独特问题，提供适合您水平的个性化学习体验\",\n        multiLanguageTitle: \"多语言支持\",\n        multiLanguageDesc: \"支持越南语、中文或英语学习，语言切换无缝衔接\",\n        progressTrackingTitle: \"进度跟踪\",\n        progressTrackingDesc: \"通过详细统计和性能分析监控您的学习历程\",\n        // Stats\n        questionsLabel: \"问题\",\n        languagesLabel: \"语言\",\n        aiPoweredLabel: \"智能学习\",\n        // Authentication\n        login: \"登录\",\n        register: \"注册\",\n        logout: \"退出\",\n        email: \"邮箱\",\n        username: \"用户名\",\n        password: \"密码\",\n        confirmPassword: \"确认密码\",\n        needAccount: \"没有账户？注册\",\n        haveAccount: \"已有账户？登录\",\n        loginRequired: \"需要登录\",\n        loginToViewHistory: \"请登录查看测验历史\",\n        // User Stats\n        stats: \"统计\",\n        totalQuestions: \"总题数\",\n        accuracy: \"准确率\",\n        streak: \"连续正确\",\n        viewHistory: \"查看历史\",\n        // History\n        quizHistory: \"测验历史\",\n        backToQuiz: \"返回测验\",\n        allLevels: \"所有级别\",\n        allResults: \"所有结果\",\n        result: \"结果\",\n        noHistory: \"未找到测验历史\",\n        previous: \"上一页\",\n        next: \"下一页\",\n        // AI Features\n        aiLessons: \"AI课程\",\n        aiReading: \"AI阅读\",\n        aiTutor: \"AI导师\",\n        createLesson: \"创建课程\",\n        generateReading: \"生成阅读\",\n        askTutor: \"询问AI导师\",\n        // Lessons\n        lessonTitle: \"AI英语课程\",\n        lessonSubtitle: \"使用AI创建个性化英语课程。选择您的主题、级别和课程类型来生成全面的学习内容。\",\n        lessonType: \"课程类型\",\n        duration: \"时长\",\n        topic: \"主题\",\n        topicPlaceholder: \"例如：一般现在时、商务英语、旅游词汇...\",\n        topicSuggestions: \"或从热门主题中选择：\",\n        generateLesson: \"生成课程\",\n        generating: \"生成中...\",\n        // Reading\n        readingTitle: \"AI阅读练习\",\n        readingSubtitle: \"使用AI生成个性化阅读文章。选择您的级别、主题和风格来创建无限练习内容。\",\n        wordCount: \"字数\",\n        style: \"风格\",\n        generatePassage: \"生成文章\",\n        vocabulary: \"词汇\",\n        comprehensionQuestions: \"理解问题\",\n        submitAnswers: \"提交答案\",\n        tryAgain: \"重试\",\n        generateNew: \"生成新文章\",\n        // Dashboard\n        dashboardTitle: \"学习仪表板\",\n        dashboardSubtitle: \"跟踪您的进度，分析您的表现，并获得个性化建议来提高您的英语。\",\n        quizzesTaken: \"已完成测验\",\n        overallPerformance: \"整体表现\",\n        streakDays: \"连续天数\",\n        studyTime: \"学习时间\",\n        progressByLevel: \"按级别进度\",\n        aiAnalysis: \"AI表现分析\",\n        strengths: \"优势\",\n        improvements: \"需要改进\",\n        nextSteps: \"下一步\",\n        motivationalMessage: \"激励信息\",\n        continuelearning: \"继续学习\",\n        // Tutor\n        tutorTitle: \"AI英语导师\",\n        tutorSubtitle: \"获得语法、词汇、发音等方面的即时帮助。用您的母语提问。\",\n        askQuestion: \"提问...\",\n        send: \"发送\",\n        grammarHelp: \"语法帮助\",\n        vocabularyHelp: \"词汇\",\n        pronunciationHelp: \"发音\",\n        writingHelp: \"写作帮助\",\n        conversationHelp: \"对话\",\n        cultureHelp: \"文化\",\n        // Footer\n        footerDescription: \"使用我们专为越南学习者设计的AI平台免费学习英语。无限选择题、结构化课程、词汇和有效的学习技巧。适合从基础到高级的所有水平。\",\n        contactAdvertising: \"广告联系\",\n        quickLinks: \"快速链接\",\n        about: \"关于我们\",\n        contact: \"联系我们\",\n        legal: \"法律\",\n        terms: \"服务条款\",\n        privacy: \"隐私政策\",\n        allRightsReserved: \"版权所有。\",\n        disclaimer: \"免责声明\",\n        disclaimerText: \"我们的网站仅提供在线英语学习服务，用于娱乐和内容分享目的。网站上发布的所有学习内容均来自互联网上的各种来源，我们对任何内容的版权或所有权不承担责任。如果您是版权所有者并认为网站上的内容侵犯了您的权利，请联系我们及时删除侵权内容。此外，我们对网站上显示的广告内容不承担责任，包括但不限于第三方产品或服务的广告。这些广告不代表我们的观点或承诺。用户在与此类广告互动时需要自行考虑并承担责任。\",\n        // Common\n        level: \"级别\",\n        back: \"返回\",\n        continue: \"继续\",\n        complete: \"完成\",\n        start: \"开始\",\n        finish: \"结束\",\n        save: \"保存\",\n        cancel: \"取消\",\n        close: \"关闭\",\n        signIn: \"登录\",\n        signOut: \"退出\",\n        signUp: \"注册\",\n        // Auth Modal\n        continueWithGoogle: \"使用Google继续\",\n        continueWithFacebook: \"使用Facebook继续\",\n        optionalLoginNote: \"登录是可选的。您可以在没有账户的情况下继续，但您的进度不会被保存。\",\n        loginError: \"使用{provider}登录失败。请重试。\",\n        // Language Switcher\n        active: \"活跃\",\n        // User Menu\n        learningDashboard: \"学习仪表板\",\n        accuracyText: \"准确率\",\n        // Error Messages\n        aiErrorMessage: \"抱歉，处理您的问题时遇到错误。请重试。\",\n        failedToGenerate: \"生成问题失败\",\n        questionGenerateError: \"抱歉，我们目前无法生成问题。请稍后重试。\",\n        tryAgainLater: \"稍后重试\",\n        backToHome: \"返回首页\",\n        // Loading States\n        processing: \"处理中...\",\n        pleaseWait: \"请稍候...\",\n        loadingHistory: \"正在加载您的测验历史...\",\n        // General Error States\n        somethingWentWrong: \"出现问题\",\n        errorOccurred: \"发生错误\",\n        retryAction: \"重试\",\n        // Additional Reading Component\n        aiGenerates: \"AI生成\",\n        aiGeneratesDesc: \"我们的AI创建独特的文章和问题\",\n        failedToGeneratePassage: \"生成阅读文章失败。请重试。\",\n        // Additional Lessons Component\n        aiCreates: \"AI创建\",\n        aiCreatesDesc: \"AI生成全面的课程内容\",\n        failedToGenerateLesson: \"生成课程失败。请重试。\",\n        // History Page\n        noHistoryYet: \"暂无历史记录\",\n        // Question Card\n        processingAnswer: \"正在处理您的答案...\",\n        chooseAnswer: \"选择您的答案\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaTE4bi90cmFuc2xhdGlvbnMudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLGVBQWU7SUFDMUJDLElBQUk7UUFDRixPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQixZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsYUFBYTtRQUViLGtCQUFrQjtRQUNsQkMsVUFBVTtRQUNWQyxjQUFjO1FBQ2RDLFVBQVU7UUFDVkMsY0FBYztRQUNkQyxrQkFBa0I7UUFDbEJDLGNBQWM7UUFFZCxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsY0FBYztRQUVkLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxnQkFBZ0I7UUFFaEIsVUFBVTtRQUNWQyxvQkFBb0I7UUFDcEJDLGtCQUFrQjtRQUNsQkMsWUFBWTtRQUNaQyxTQUFTO1FBRVQsUUFBUTtRQUNSQyxZQUFZO1FBRVosb0JBQW9CO1FBQ3BCQyxVQUFVO1FBQ1ZDLFNBQVM7UUFDVEMsWUFBWTtRQUNaQyxTQUFTO1FBRVQsbUJBQW1CO1FBQ25CQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtRQUNuQkMsa0JBQWtCO1FBQ2xCQyxpQkFBaUI7UUFDakJDLG9CQUFvQjtRQUNwQkMsbUJBQW1CO1FBQ25CQyx1QkFBdUI7UUFDdkJDLHNCQUFzQjtRQUV0QixRQUFRO1FBQ1JDLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFFaEIsaUJBQWlCO1FBQ2pCQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLG9CQUFvQjtRQUVwQixhQUFhO1FBQ2JDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsYUFBYTtRQUViLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsWUFBWTtRQUNaQyxRQUFRO1FBQ1JDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxNQUFNO1FBRU4sY0FBYztRQUNkQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsVUFBVTtRQUVWLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLGtCQUFrQjtRQUNsQkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFFWixVQUFVO1FBQ1ZDLGNBQWM7UUFDZEMsaUJBQWlCO1FBQ2pCQyxXQUFXO1FBQ1hDLE9BQU87UUFDUEMsaUJBQWlCO1FBQ2pCQyxZQUFZO1FBQ1pDLHdCQUF3QjtRQUN4QkMsZUFBZTtRQUNmQyxVQUFVO1FBQ1ZDLGFBQWE7UUFFYixZQUFZO1FBQ1pDLGdCQUFnQjtRQUNoQkMsbUJBQW1CO1FBQ25CQyxjQUFjO1FBQ2RDLG9CQUFvQjtRQUNwQkMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxxQkFBcUI7UUFDckJDLGtCQUFrQjtRQUVsQixRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtRQUNuQkMsYUFBYTtRQUNiQyxrQkFBa0I7UUFDbEJDLGFBQWE7UUFFYixTQUFTO1FBQ1RDLG1CQUFtQjtRQUNuQkMsb0JBQW9CO1FBQ3BCQyxZQUFZO1FBQ1pDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLFlBQVk7UUFDWkMsZ0JBQWdCO1FBRWhCLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLFFBQVE7UUFFUixhQUFhO1FBQ2JDLG9CQUFvQjtRQUNwQkMsc0JBQXNCO1FBQ3RCQyxtQkFBbUI7UUFDbkJDLFlBQVk7UUFFWixvQkFBb0I7UUFDcEJDLFFBQVE7UUFFUixZQUFZO1FBQ1pDLG1CQUFtQjtRQUNuQkMsY0FBYztRQUVkLGlCQUFpQjtRQUNqQkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLHVCQUF1QjtRQUN2QkMsZUFBZTtRQUNmQyxZQUFZO1FBRVosaUJBQWlCO1FBQ2pCQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsZ0JBQWdCO1FBRWhCLHVCQUF1QjtRQUN2QkMsb0JBQW9CO1FBQ3BCQyxlQUFlO1FBQ2ZDLGFBQWE7UUFFYiwrQkFBK0I7UUFDL0JDLGFBQWE7UUFDYkMsaUJBQWlCO1FBQ2pCQyx5QkFBeUI7UUFFekIsK0JBQStCO1FBQy9CQyxXQUFXO1FBQ1hDLGVBQWU7UUFDZkMsd0JBQXdCO1FBRXhCLGVBQWU7UUFDZkMsY0FBYztRQUVkLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyxjQUFjO0lBQ2hCO0lBRUFDLElBQUk7UUFDRixPQUFPO1FBQ1BwSyxnQkFBZ0I7UUFDaEIsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLGFBQWE7UUFFYixrQkFBa0I7UUFDbEJDLFVBQVU7UUFDVkMsY0FBYztRQUNkQyxVQUFVO1FBQ1ZDLGNBQWM7UUFDZEMsa0JBQWtCO1FBQ2xCQyxjQUFjO1FBRWQsYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGNBQWM7UUFFZCxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLGNBQWM7UUFDZEMsZ0JBQWdCO1FBRWhCLFVBQVU7UUFDVkMsb0JBQW9CO1FBQ3BCQyxrQkFBa0I7UUFDbEJDLFlBQVk7UUFDWkMsU0FBUztRQUVULFFBQVE7UUFDUkMsWUFBWTtRQUVaLG9CQUFvQjtRQUNwQkMsVUFBVTtRQUNWQyxTQUFTO1FBQ1RDLFlBQVk7UUFDWkMsU0FBUztRQUVULG1CQUFtQjtRQUNuQkMsZ0JBQWdCO1FBQ2hCQyxtQkFBbUI7UUFDbkJDLGtCQUFrQjtRQUNsQkMsaUJBQWlCO1FBQ2pCQyxvQkFBb0I7UUFDcEJDLG1CQUFtQjtRQUNuQkMsdUJBQXVCO1FBQ3ZCQyxzQkFBc0I7UUFFdEIsUUFBUTtRQUNSQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBRWhCLGlCQUFpQjtRQUNqQkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsaUJBQWlCO1FBQ2pCQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxvQkFBb0I7UUFFcEIsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLGFBQWE7UUFFYixVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsUUFBUTtRQUNSQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsTUFBTTtRQUVOLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsY0FBYztRQUNkQyxpQkFBaUI7UUFDakJDLFVBQVU7UUFFVixVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO1FBQ1pDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO1FBRVosVUFBVTtRQUNWQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsV0FBVztRQUNYQyxPQUFPO1FBQ1BDLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyx3QkFBd0I7UUFDeEJDLGVBQWU7UUFDZkMsVUFBVTtRQUNWQyxhQUFhO1FBRWIsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtRQUNuQkMsY0FBYztRQUNkQyxvQkFBb0I7UUFDcEJDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxpQkFBaUI7UUFDakJDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFdBQVc7UUFDWEMscUJBQXFCO1FBQ3JCQyxrQkFBa0I7UUFFbEIsUUFBUTtRQUNSQyxZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsZ0JBQWdCO1FBQ2hCQyxtQkFBbUI7UUFDbkJDLGFBQWE7UUFDYkMsa0JBQWtCO1FBQ2xCQyxhQUFhO1FBRWIsU0FBUztRQUNUQyxtQkFBbUI7UUFDbkJDLG9CQUFvQjtRQUNwQkMsWUFBWTtRQUNaQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsbUJBQW1CO1FBQ25CQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUVoQixTQUFTO1FBQ1RDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxRQUFRO1FBRVIsYUFBYTtRQUNiQyxvQkFBb0I7UUFDcEJDLHNCQUFzQjtRQUN0QkMsbUJBQW1CO1FBQ25CQyxZQUFZO1FBRVosb0JBQW9CO1FBQ3BCQyxRQUFRO1FBRVIsWUFBWTtRQUNaQyxtQkFBbUI7UUFDbkJDLGNBQWM7UUFFZCxpQkFBaUI7UUFDakJDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyx1QkFBdUI7UUFDdkJDLGVBQWU7UUFDZkMsWUFBWTtRQUVaLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGdCQUFnQjtRQUVoQix1QkFBdUI7UUFDdkJDLG9CQUFvQjtRQUNwQkMsZUFBZTtRQUNmQyxhQUFhO1FBRWIsK0JBQStCO1FBQy9CQyxhQUFhO1FBQ2JDLGlCQUFpQjtRQUNqQkMseUJBQXlCO1FBRXpCLCtCQUErQjtRQUMvQkMsV0FBVztRQUNYQyxlQUFlO1FBQ2ZDLHdCQUF3QjtRQUV4QixlQUFlO1FBQ2ZDLGNBQWM7UUFFZCxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsY0FBYztJQUNoQjtJQUVBRSxJQUFJO1FBQ0YsT0FBTztRQUNQckssZ0JBQWdCO1FBQ2hCLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxhQUFhO1FBRWIsa0JBQWtCO1FBQ2xCQyxVQUFVO1FBQ1ZDLGNBQWM7UUFDZEMsVUFBVTtRQUNWQyxjQUFjO1FBQ2RDLGtCQUFrQjtRQUNsQkMsY0FBYztRQUVkLGFBQWE7UUFDYkMsY0FBYztRQUNkQyxjQUFjO1FBRWQsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLGdCQUFnQjtRQUVoQixVQUFVO1FBQ1ZDLG9CQUFvQjtRQUNwQkMsa0JBQWtCO1FBQ2xCQyxZQUFZO1FBQ1pDLFNBQVM7UUFFVCxRQUFRO1FBQ1JDLFlBQVk7UUFFWixvQkFBb0I7UUFDcEJDLFVBQVU7UUFDVkMsU0FBUztRQUNUQyxZQUFZO1FBQ1pDLFNBQVM7UUFFVCxtQkFBbUI7UUFDbkJDLGdCQUFnQjtRQUNoQkMsbUJBQW1CO1FBQ25CQyxrQkFBa0I7UUFDbEJDLGlCQUFpQjtRQUNqQkMsb0JBQW9CO1FBQ3BCQyxtQkFBbUI7UUFDbkJDLHVCQUF1QjtRQUN2QkMsc0JBQXNCO1FBRXRCLFFBQVE7UUFDUkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUVoQixpQkFBaUI7UUFDakJDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGlCQUFpQjtRQUNqQkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsb0JBQW9CO1FBRXBCLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBRWIsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxVQUFVO1FBQ1ZDLE1BQU07UUFFTixjQUFjO1FBQ2RDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLGNBQWM7UUFDZEMsaUJBQWlCO1FBQ2pCQyxVQUFVO1FBRVYsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsa0JBQWtCO1FBQ2xCQyxrQkFBa0I7UUFDbEJDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUVaLFVBQVU7UUFDVkMsY0FBYztRQUNkQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFDWEMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLFlBQVk7UUFDWkMsd0JBQXdCO1FBQ3hCQyxlQUFlO1FBQ2ZDLFVBQVU7UUFDVkMsYUFBYTtRQUViLFlBQVk7UUFDWkMsZ0JBQWdCO1FBQ2hCQyxtQkFBbUI7UUFDbkJDLGNBQWM7UUFDZEMsb0JBQW9CO1FBQ3BCQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsaUJBQWlCO1FBQ2pCQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLHFCQUFxQjtRQUNyQkMsa0JBQWtCO1FBRWxCLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLGdCQUFnQjtRQUNoQkMsbUJBQW1CO1FBQ25CQyxhQUFhO1FBQ2JDLGtCQUFrQjtRQUNsQkMsYUFBYTtRQUViLFNBQVM7UUFDVEMsbUJBQW1CO1FBQ25CQyxvQkFBb0I7UUFDcEJDLFlBQVk7UUFDWkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLG1CQUFtQjtRQUNuQkMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFFaEIsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtRQUVSLGFBQWE7UUFDYkMsb0JBQW9CO1FBQ3BCQyxzQkFBc0I7UUFDdEJDLG1CQUFtQjtRQUNuQkMsWUFBWTtRQUVaLG9CQUFvQjtRQUNwQkMsUUFBUTtRQUVSLFlBQVk7UUFDWkMsbUJBQW1CO1FBQ25CQyxjQUFjO1FBRWQsaUJBQWlCO1FBQ2pCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsdUJBQXVCO1FBQ3ZCQyxlQUFlO1FBQ2ZDLFlBQVk7UUFFWixpQkFBaUI7UUFDakJDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFFaEIsdUJBQXVCO1FBQ3ZCQyxvQkFBb0I7UUFDcEJDLGVBQWU7UUFDZkMsYUFBYTtRQUViLCtCQUErQjtRQUMvQkMsYUFBYTtRQUNiQyxpQkFBaUI7UUFDakJDLHlCQUF5QjtRQUV6QiwrQkFBK0I7UUFDL0JDLFdBQVc7UUFDWEMsZUFBZTtRQUNmQyx3QkFBd0I7UUFFeEIsZUFBZTtRQUNmQyxjQUFjO1FBRWQsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGNBQWM7SUFDaEI7QUFDRixFQUFFIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxzcmNcXGkxOG5cXHRyYW5zbGF0aW9ucy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdHJhbnNsYXRpb25zID0ge1xuICBlbjoge1xuICAgIC8vTG9naW5cbiAgICB3ZWxjb21lTWVzc2FnZTogXCJTaWduIGluIHRvIHRyYWNrIHlvdXIgcHJvZ3Jlc3MgYW5kIGNvbnRpbnVlIGxlYXJuaW5nXCIsXG4gICAgLy8gQXBwIFRpdGxlXG4gICAgYXBwVGl0bGU6IFwiSW5maW5pdGUgRW5nbGlzaFwiLFxuICAgIGFwcFN1YnRpdGxlOiBcIkxlYXJuIEVuZ2xpc2ggd2l0aCBBSS1wb3dlcmVkIGxlc3NvbnMgYW5kIHF1aXp6ZXNcIixcbiAgICBcbiAgICAvLyBMZXZlbCBTZWxlY3Rpb25cbiAgICBiZWdpbm5lcjogXCJCZWdpbm5lclwiLFxuICAgIGludGVybWVkaWF0ZTogXCJJbnRlcm1lZGlhdGVcIiwgXG4gICAgYWR2YW5jZWQ6IFwiQWR2YW5jZWRcIixcbiAgICBiZWdpbm5lckRlc2M6IFwiQmFzaWMgdm9jYWJ1bGFyeSBhbmQgc2ltcGxlIGdyYW1tYXJcIixcbiAgICBpbnRlcm1lZGlhdGVEZXNjOiBcIk1vcmUgY29tcGxleCBzZW50ZW5jZXMgYW5kIHZvY2FidWxhcnlcIixcbiAgICBhZHZhbmNlZERlc2M6IFwiQ29tcGxleCBncmFtbWFyIGFuZCBhZHZhbmNlZCB2b2NhYnVsYXJ5XCIsXG4gICAgXG4gICAgLy8gTmF2aWdhdGlvblxuICAgIGJhY2tUb0xldmVsczogXCLihpAgQmFjayB0byBMZXZlbHNcIixcbiAgICBuZXh0UXVlc3Rpb246IFwiTmV4dCBRdWVzdGlvblwiLFxuICAgIFxuICAgIC8vIFF1aXpcbiAgICBzY29yZTogXCJTY29yZVwiLFxuICAgIGNvcnJlY3Q6IFwiQ29ycmVjdCFcIixcbiAgICBpbmNvcnJlY3Q6IFwiSW5jb3JyZWN0IVwiLFxuICAgIHF1ZXN0aW9uOiBcIlF1ZXN0aW9uOlwiLFxuICAgIHlvdXJBbnN3ZXI6IFwiWW91ciBhbnN3ZXI6XCIsXG4gICAgY29ycmVjdEFuc3dlcjogXCJDb3JyZWN0IGFuc3dlcjpcIixcbiAgICBleHBsYW5hdGlvbjogXCJFeHBsYW5hdGlvbjpcIixcbiAgICBhbnN3ZXJzOiBcIkFuc3dlcnNcIixcbiAgICB0b3RhbDogXCJUb3RhbFwiLFxuICAgIHNlbGVjdEFuc3dlcjogXCJTZWxlY3QgeW91ciBhbnN3ZXJcIixcbiAgICBhbnN3ZXJTZWxlY3RlZDogXCJBbnN3ZXIgc2VsZWN0ZWQhIENoZWNraW5nLi4uXCIsXG5cbiAgICAvLyBMb2FkaW5nXG4gICAgZ2VuZXJhdGluZ1F1ZXN0aW9uOiBcIkdlbmVyYXRpbmcgeW91ciBxdWVzdGlvbi4uLlwiLFxuICAgIGNyZWF0aW5nUXVlc3Rpb246IFwiQ3JlYXRpbmcgYSBwZXJzb25hbGl6ZWQgcXVlc3Rpb24ganVzdCBmb3IgeW91XCIsXG4gICAgbmF2aWdhdGluZzogXCJOYXZpZ2F0aW5nLi4uXCIsXG4gICAgbG9hZGluZzogXCJMb2FkaW5nLi4uXCIsXG5cbiAgICAvLyBTY29yZVxuICAgIHJlc2V0U2NvcmU6IFwiUmVzZXQgU2NvcmVcIixcblxuICAgIC8vIExhbmd1YWdlIFN3aXRjaGVyXG4gICAgbGFuZ3VhZ2U6IFwiTGFuZ3VhZ2VcIixcbiAgICBlbmdsaXNoOiBcIkVuZ2xpc2hcIixcbiAgICB2aWV0bmFtZXNlOiBcIlRp4bq/bmcgVmnhu4d0XCIsXG4gICAgY2hpbmVzZTogXCLkuK3mlodcIixcblxuICAgIC8vIEZlYXR1cmVzIFNlY3Rpb25cbiAgICB3aHlDaG9vc2VUaXRsZTogXCJXaHkgQ2hvb3NlIEluZmluaXRlIEVuZ2xpc2g/XCIsXG4gICAgd2h5Q2hvb3NlU3VidGl0bGU6IFwiRXhwZXJpZW5jZSB0aGUgZnV0dXJlIG9mIGxhbmd1YWdlIGxlYXJuaW5nIHdpdGggb3VyIGN1dHRpbmctZWRnZSBmZWF0dXJlc1wiLFxuICAgIGFpR2VuZXJhdGVkVGl0bGU6IFwiVW5saW1pdGVkIFF1ZXN0aW9uc1wiLFxuICAgIGFpR2VuZXJhdGVkRGVzYzogXCJFbmRsZXNzIHVuaXF1ZSBxdWVzdGlvbnMgd2l0aCBwZXJzb25hbGl6ZWQgbGVhcm5pbmcgZXhwZXJpZW5jZSB0YWlsb3JlZCB0byB5b3VyIGxldmVsXCIsXG4gICAgbXVsdGlMYW5ndWFnZVRpdGxlOiBcIk11bHRpLUxhbmd1YWdlIFN1cHBvcnRcIixcbiAgICBtdWx0aUxhbmd1YWdlRGVzYzogXCJMZWFybiBpbiBWaWV0bmFtZXNlLCBDaGluZXNlLCBvciBFbmdsaXNoIHdpdGggc2VhbWxlc3MgbGFuZ3VhZ2Ugc3dpdGNoaW5nXCIsXG4gICAgcHJvZ3Jlc3NUcmFja2luZ1RpdGxlOiBcIlByb2dyZXNzIFRyYWNraW5nXCIsXG4gICAgcHJvZ3Jlc3NUcmFja2luZ0Rlc2M6IFwiTW9uaXRvciB5b3VyIGxlYXJuaW5nIGpvdXJuZXkgd2l0aCBkZXRhaWxlZCBzdGF0aXN0aWNzIGFuZCBwZXJmb3JtYW5jZSBhbmFseXRpY3NcIixcblxuICAgIC8vIFN0YXRzXG4gICAgcXVlc3Rpb25zTGFiZWw6IFwiUXVlc3Rpb25zXCIsXG4gICAgbGFuZ3VhZ2VzTGFiZWw6IFwiTGFuZ3VhZ2VzXCIsXG4gICAgYWlQb3dlcmVkTGFiZWw6IFwiU21hcnQgTGVhcm5pbmdcIixcblxuICAgIC8vIEF1dGhlbnRpY2F0aW9uXG4gICAgbG9naW46IFwiTG9naW5cIixcbiAgICByZWdpc3RlcjogXCJSZWdpc3RlclwiLFxuICAgIGxvZ291dDogXCJMb2dvdXRcIixcbiAgICBlbWFpbDogXCJFbWFpbFwiLFxuICAgIHVzZXJuYW1lOiBcIlVzZXJuYW1lXCIsXG4gICAgcGFzc3dvcmQ6IFwiUGFzc3dvcmRcIixcbiAgICBjb25maXJtUGFzc3dvcmQ6IFwiQ29uZmlybSBQYXNzd29yZFwiLFxuICAgIG5lZWRBY2NvdW50OiBcIkRvbid0IGhhdmUgYW4gYWNjb3VudD8gUmVnaXN0ZXJcIixcbiAgICBoYXZlQWNjb3VudDogXCJBbHJlYWR5IGhhdmUgYW4gYWNjb3VudD8gTG9naW5cIixcbiAgICBsb2dpblJlcXVpcmVkOiBcIkxvZ2luIFJlcXVpcmVkXCIsXG4gICAgbG9naW5Ub1ZpZXdIaXN0b3J5OiBcIlBsZWFzZSBsb2dpbiB0byB2aWV3IHlvdXIgcXVpeiBoaXN0b3J5XCIsXG5cbiAgICAvLyBVc2VyIFN0YXRzXG4gICAgc3RhdHM6IFwiU3RhdGlzdGljc1wiLFxuICAgIHRvdGFsUXVlc3Rpb25zOiBcIlRvdGFsIFF1ZXN0aW9uc1wiLFxuICAgIGFjY3VyYWN5OiBcIkFjY3VyYWN5XCIsXG4gICAgc3RyZWFrOiBcIlN0cmVha1wiLFxuICAgIHZpZXdIaXN0b3J5OiBcIlZpZXcgSGlzdG9yeVwiLFxuXG4gICAgLy8gSGlzdG9yeVxuICAgIHF1aXpIaXN0b3J5OiBcIlF1aXogSGlzdG9yeVwiLFxuICAgIGJhY2tUb1F1aXo6IFwiQmFjayB0byBRdWl6XCIsXG4gICAgYWxsTGV2ZWxzOiBcIkFsbCBMZXZlbHNcIixcbiAgICBhbGxSZXN1bHRzOiBcIkFsbCBSZXN1bHRzXCIsXG4gICAgcmVzdWx0OiBcIlJlc3VsdFwiLFxuICAgIG5vSGlzdG9yeTogXCJObyBxdWl6IGhpc3RvcnkgZm91bmRcIixcbiAgICBwcmV2aW91czogXCJQcmV2aW91c1wiLFxuICAgIG5leHQ6IFwiTmV4dFwiLFxuXG4gICAgLy8gQUkgRmVhdHVyZXNcbiAgICBhaUxlc3NvbnM6IFwiQUkgTGVzc29uc1wiLFxuICAgIGFpUmVhZGluZzogXCJBSSBSZWFkaW5nXCIsXG4gICAgYWlUdXRvcjogXCJBSSBUdXRvclwiLFxuICAgIGNyZWF0ZUxlc3NvbjogXCJDcmVhdGUgTGVzc29uXCIsXG4gICAgZ2VuZXJhdGVSZWFkaW5nOiBcIkdlbmVyYXRlIFJlYWRpbmdcIixcbiAgICBhc2tUdXRvcjogXCJBc2sgQUkgVHV0b3JcIixcblxuICAgIC8vIExlc3NvbnNcbiAgICBsZXNzb25UaXRsZTogXCJBSSBFbmdsaXNoIExlc3NvbnNcIixcbiAgICBsZXNzb25TdWJ0aXRsZTogXCJDcmVhdGUgcGVyc29uYWxpemVkIEVuZ2xpc2ggbGVzc29ucyB3aXRoIEFJLiBDaG9vc2UgeW91ciB0b3BpYywgbGV2ZWwsIGFuZCBsZXNzb24gdHlwZSB0byBnZW5lcmF0ZSBjb21wcmVoZW5zaXZlIGxlYXJuaW5nIGNvbnRlbnQuXCIsXG4gICAgbGVzc29uVHlwZTogXCJMZXNzb24gVHlwZVwiLFxuICAgIGR1cmF0aW9uOiBcIkR1cmF0aW9uXCIsXG4gICAgdG9waWM6IFwiVG9waWNcIixcbiAgICB0b3BpY1BsYWNlaG9sZGVyOiBcImUuZy4sIFByZXNlbnQgU2ltcGxlIFRlbnNlLCBCdXNpbmVzcyBFbmdsaXNoLCBUcmF2ZWwgVm9jYWJ1bGFyeS4uLlwiLFxuICAgIHRvcGljU3VnZ2VzdGlvbnM6IFwiT3IgY2hvb3NlIGZyb20gcG9wdWxhciB0b3BpY3M6XCIsXG4gICAgZ2VuZXJhdGVMZXNzb246IFwiR2VuZXJhdGUgTGVzc29uXCIsXG4gICAgZ2VuZXJhdGluZzogXCJHZW5lcmF0aW5nLi4uXCIsXG5cbiAgICAvLyBSZWFkaW5nXG4gICAgcmVhZGluZ1RpdGxlOiBcIkFJIFJlYWRpbmcgUHJhY3RpY2VcIixcbiAgICByZWFkaW5nU3VidGl0bGU6IFwiR2VuZXJhdGUgcGVyc29uYWxpemVkIHJlYWRpbmcgcGFzc2FnZXMgd2l0aCBBSS4gQ2hvb3NlIHlvdXIgbGV2ZWwsIHRvcGljLCBhbmQgc3R5bGUgdG8gY3JlYXRlIHVubGltaXRlZCBwcmFjdGljZSBjb250ZW50LlwiLFxuICAgIHdvcmRDb3VudDogXCJXb3JkIENvdW50XCIsXG4gICAgc3R5bGU6IFwiU3R5bGVcIixcbiAgICBnZW5lcmF0ZVBhc3NhZ2U6IFwiR2VuZXJhdGUgUGFzc2FnZVwiLFxuICAgIHZvY2FidWxhcnk6IFwiVm9jYWJ1bGFyeVwiLFxuICAgIGNvbXByZWhlbnNpb25RdWVzdGlvbnM6IFwiQ29tcHJlaGVuc2lvbiBRdWVzdGlvbnNcIixcbiAgICBzdWJtaXRBbnN3ZXJzOiBcIlN1Ym1pdCBBbnN3ZXJzXCIsXG4gICAgdHJ5QWdhaW46IFwiVHJ5IEFnYWluXCIsXG4gICAgZ2VuZXJhdGVOZXc6IFwiR2VuZXJhdGUgTmV3IFBhc3NhZ2VcIixcblxuICAgIC8vIERhc2hib2FyZFxuICAgIGRhc2hib2FyZFRpdGxlOiBcIkxlYXJuaW5nIERhc2hib2FyZFwiLFxuICAgIGRhc2hib2FyZFN1YnRpdGxlOiBcIlRyYWNrIHlvdXIgcHJvZ3Jlc3MsIGFuYWx5emUgeW91ciBwZXJmb3JtYW5jZSwgYW5kIGdldCBwZXJzb25hbGl6ZWQgcmVjb21tZW5kYXRpb25zIHRvIGltcHJvdmUgeW91ciBFbmdsaXNoLlwiLFxuICAgIHF1aXp6ZXNUYWtlbjogXCJRdWl6emVzIFRha2VuXCIsXG4gICAgb3ZlcmFsbFBlcmZvcm1hbmNlOiBcIk92ZXJhbGwgUGVyZm9ybWFuY2VcIixcbiAgICBzdHJlYWtEYXlzOiBcIkRheSBTdHJlYWtcIixcbiAgICBzdHVkeVRpbWU6IFwiU3R1ZHkgVGltZVwiLFxuICAgIHByb2dyZXNzQnlMZXZlbDogXCJQcm9ncmVzcyBieSBMZXZlbFwiLFxuICAgIGFpQW5hbHlzaXM6IFwiQUkgUGVyZm9ybWFuY2UgQW5hbHlzaXNcIixcbiAgICBzdHJlbmd0aHM6IFwiU3RyZW5ndGhzXCIsXG4gICAgaW1wcm92ZW1lbnRzOiBcIkFyZWFzIGZvciBJbXByb3ZlbWVudFwiLFxuICAgIG5leHRTdGVwczogXCJOZXh0IFN0ZXBzXCIsXG4gICAgbW90aXZhdGlvbmFsTWVzc2FnZTogXCJNb3RpdmF0aW9uYWwgTWVzc2FnZVwiLFxuICAgIGNvbnRpbnVlbGVhcm5pbmc6IFwiQ29udGludWUgTGVhcm5pbmdcIixcblxuICAgIC8vIFR1dG9yXG4gICAgdHV0b3JUaXRsZTogXCJBSSBFbmdsaXNoIFR1dG9yXCIsXG4gICAgdHV0b3JTdWJ0aXRsZTogXCJHZXQgaW5zdGFudCBoZWxwIHdpdGggZ3JhbW1hciwgdm9jYWJ1bGFyeSwgcHJvbnVuY2lhdGlvbiwgYW5kIG1vcmUuIEFzayBxdWVzdGlvbnMgaW4geW91ciBuYXRpdmUgbGFuZ3VhZ2UuXCIsXG4gICAgYXNrUXVlc3Rpb246IFwiQXNrIGEgcXVlc3Rpb24uLi5cIixcbiAgICBzZW5kOiBcIlNlbmRcIixcbiAgICBncmFtbWFySGVscDogXCJHcmFtbWFyIEhlbHBcIixcbiAgICB2b2NhYnVsYXJ5SGVscDogXCJWb2NhYnVsYXJ5XCIsXG4gICAgcHJvbnVuY2lhdGlvbkhlbHA6IFwiUHJvbnVuY2lhdGlvblwiLFxuICAgIHdyaXRpbmdIZWxwOiBcIldyaXRpbmcgSGVscFwiLFxuICAgIGNvbnZlcnNhdGlvbkhlbHA6IFwiQ29udmVyc2F0aW9uXCIsXG4gICAgY3VsdHVyZUhlbHA6IFwiQ3VsdHVyZVwiLFxuXG4gICAgLy8gRm9vdGVyXG4gICAgZm9vdGVyRGVzY3JpcHRpb246IFwiTGVhcm4gRW5nbGlzaCBmb3IgZnJlZSB3aXRoIG91ciBBSS1wb3dlcmVkIHBsYXRmb3JtIGRlc2lnbmVkIGZvciBWaWV0bmFtZXNlIGxlYXJuZXJzLiBVbmxpbWl0ZWQgbXVsdGlwbGUgY2hvaWNlIHF1ZXN0aW9ucywgc3RydWN0dXJlZCBsZXNzb25zLCB2b2NhYnVsYXJ5IGFuZCBlZmZlY3RpdmUgbGVhcm5pbmcgdGlwcy4gU3VpdGFibGUgZm9yIGFsbCBsZXZlbHMgZnJvbSBiYXNpYyB0byBhZHZhbmNlZC5cIixcbiAgICBjb250YWN0QWR2ZXJ0aXNpbmc6IFwiQ29udGFjdCBmb3IgQWR2ZXJ0aXNpbmdcIixcbiAgICBxdWlja0xpbmtzOiBcIlF1aWNrIExpbmtzXCIsXG4gICAgYWJvdXQ6IFwiQWJvdXRcIixcbiAgICBjb250YWN0OiBcIkNvbnRhY3RcIixcbiAgICBsZWdhbDogXCJMZWdhbFwiLFxuICAgIHRlcm1zOiBcIlRlcm1zIG9mIFNlcnZpY2VcIixcbiAgICBwcml2YWN5OiBcIlByaXZhY3kgUG9saWN5XCIsXG4gICAgYWxsUmlnaHRzUmVzZXJ2ZWQ6IFwiQWxsIHJpZ2h0cyByZXNlcnZlZC5cIixcbiAgICBkaXNjbGFpbWVyOiBcIkRpc2NsYWltZXJcIixcbiAgICBkaXNjbGFpbWVyVGV4dDogXCJPdXIgd2Vic2l0ZSBvbmx5IHByb3ZpZGVzIG9ubGluZSBFbmdsaXNoIGxlYXJuaW5nIHNlcnZpY2VzIGZvciBlbnRlcnRhaW5tZW50IGFuZCBjb250ZW50IHNoYXJpbmcgcHVycG9zZXMuIEFsbCBsZWFybmluZyBjb250ZW50IHBvc3RlZCBvbiB0aGUgd2Vic2l0ZSBpcyBjb2xsZWN0ZWQgZnJvbSB2YXJpb3VzIHNvdXJjZXMgb24gdGhlIGludGVybmV0IGFuZCB3ZSBhcmUgbm90IHJlc3BvbnNpYmxlIGZvciBjb3B5cmlnaHQgb3Igb3duZXJzaGlwIG9mIGFueSBjb250ZW50LiBJZiB5b3UgYXJlIGEgY29weXJpZ2h0IG93bmVyIGFuZCBiZWxpZXZlIHRoYXQgY29udGVudCBvbiB0aGUgc2l0ZSB2aW9sYXRlcyB5b3VyIHJpZ2h0cywgcGxlYXNlIGNvbnRhY3QgdXMgdG8gcmVtb3ZlIHRoZSBpbmZyaW5naW5nIGNvbnRlbnQgcHJvbXB0bHkuIEluIGFkZGl0aW9uLCB3ZSBhcmUgbm90IHJlc3BvbnNpYmxlIGZvciBhZHZlcnRpc2luZyBjb250ZW50IGRpc3BsYXllZCBvbiB0aGUgd2Vic2l0ZSwgaW5jbHVkaW5nIGJ1dCBub3QgbGltaXRlZCB0byBhZHZlcnRpc2luZyBwcm9kdWN0cyBvciBzZXJ2aWNlcyBvZiB0aGlyZCBwYXJ0aWVzLiBUaGVzZSBhZHZlcnRpc2VtZW50cyBkbyBub3QgcmVmbGVjdCBvdXIgdmlld3Mgb3IgY29tbWl0bWVudHMuIFVzZXJzIG5lZWQgdG8gY29uc2lkZXIgYW5kIHRha2UgcmVzcG9uc2liaWxpdHkgd2hlbiBpbnRlcmFjdGluZyB3aXRoIHN1Y2ggYWR2ZXJ0aXNlbWVudHMuXCIsXG5cbiAgICAvLyBDb21tb25cbiAgICBsZXZlbDogXCJMZXZlbFwiLFxuICAgIGJhY2s6IFwiQmFja1wiLFxuICAgIGNvbnRpbnVlOiBcIkNvbnRpbnVlXCIsXG4gICAgY29tcGxldGU6IFwiQ29tcGxldGVcIixcbiAgICBzdGFydDogXCJTdGFydFwiLFxuICAgIGZpbmlzaDogXCJGaW5pc2hcIixcbiAgICBzYXZlOiBcIlNhdmVcIixcbiAgICBjYW5jZWw6IFwiQ2FuY2VsXCIsXG4gICAgY2xvc2U6IFwiQ2xvc2VcIixcbiAgICBzaWduSW46IFwiU2lnbiBJblwiLFxuICAgIHNpZ25PdXQ6IFwiU2lnbiBPdXRcIixcbiAgICBzaWduVXA6IFwiU2lnbiBVcFwiLFxuXG4gICAgLy8gQXV0aCBNb2RhbFxuICAgIGNvbnRpbnVlV2l0aEdvb2dsZTogXCJDb250aW51ZSB3aXRoIEdvb2dsZVwiLFxuICAgIGNvbnRpbnVlV2l0aEZhY2Vib29rOiBcIkNvbnRpbnVlIHdpdGggRmFjZWJvb2tcIixcbiAgICBvcHRpb25hbExvZ2luTm90ZTogXCJMb2dpbiBpcyBvcHRpb25hbC4gWW91IGNhbiBjb250aW51ZSB3aXRob3V0IGFuIGFjY291bnQsIGJ1dCB5b3VyIHByb2dyZXNzIHdvbid0IGJlIHNhdmVkLlwiLFxuICAgIGxvZ2luRXJyb3I6IFwiRmFpbGVkIHRvIGxvZ2luIHdpdGgge3Byb3ZpZGVyfS4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcblxuICAgIC8vIExhbmd1YWdlIFN3aXRjaGVyXG4gICAgYWN0aXZlOiBcIkFjdGl2ZVwiLFxuXG4gICAgLy8gVXNlciBNZW51XG4gICAgbGVhcm5pbmdEYXNoYm9hcmQ6IFwiTGVhcm5pbmcgRGFzaGJvYXJkXCIsXG4gICAgYWNjdXJhY3lUZXh0OiBcImFjY3VyYWN5XCIsXG5cbiAgICAvLyBFcnJvciBNZXNzYWdlc1xuICAgIGFpRXJyb3JNZXNzYWdlOiBcIlNvcnJ5LCBJIGVuY291bnRlcmVkIGFuIGVycm9yIHdoaWxlIHByb2Nlc3NpbmcgeW91ciBxdWVzdGlvbi4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcbiAgICBmYWlsZWRUb0dlbmVyYXRlOiBcIkZhaWxlZCB0byBHZW5lcmF0ZSBRdWVzdGlvblwiLFxuICAgIHF1ZXN0aW9uR2VuZXJhdGVFcnJvcjogXCJTb3JyeSwgd2UgY291bGRuJ3QgZ2VuZXJhdGUgYSBxdWVzdGlvbiBhdCB0aGlzIHRpbWUuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuXCIsXG4gICAgdHJ5QWdhaW5MYXRlcjogXCJUcnkgQWdhaW4gTGF0ZXJcIixcbiAgICBiYWNrVG9Ib21lOiBcIkJhY2sgdG8gSG9tZVwiLFxuXG4gICAgLy8gTG9hZGluZyBTdGF0ZXNcbiAgICBwcm9jZXNzaW5nOiBcIlByb2Nlc3NpbmcuLi5cIixcbiAgICBwbGVhc2VXYWl0OiBcIlBsZWFzZSB3YWl0Li4uXCIsXG4gICAgbG9hZGluZ0hpc3Rvcnk6IFwiTG9hZGluZyB5b3VyIHF1aXogaGlzdG9yeS4uLlwiLFxuXG4gICAgLy8gR2VuZXJhbCBFcnJvciBTdGF0ZXNcbiAgICBzb21ldGhpbmdXZW50V3Jvbmc6IFwiU29tZXRoaW5nIHdlbnQgd3JvbmdcIixcbiAgICBlcnJvck9jY3VycmVkOiBcIkFuIGVycm9yIG9jY3VycmVkXCIsXG4gICAgcmV0cnlBY3Rpb246IFwiUmV0cnlcIixcblxuICAgIC8vIEFkZGl0aW9uYWwgUmVhZGluZyBDb21wb25lbnRcbiAgICBhaUdlbmVyYXRlczogXCJBSSBHZW5lcmF0ZXNcIixcbiAgICBhaUdlbmVyYXRlc0Rlc2M6IFwiT3VyIEFJIGNyZWF0ZXMgYSB1bmlxdWUgcGFzc2FnZSB3aXRoIHF1ZXN0aW9uc1wiLFxuICAgIGZhaWxlZFRvR2VuZXJhdGVQYXNzYWdlOiBcIkZhaWxlZCB0byBnZW5lcmF0ZSByZWFkaW5nIHBhc3NhZ2UuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG5cbiAgICAvLyBBZGRpdGlvbmFsIExlc3NvbnMgQ29tcG9uZW50XG4gICAgYWlDcmVhdGVzOiBcIkFJIENyZWF0ZXNcIixcbiAgICBhaUNyZWF0ZXNEZXNjOiBcIkFJIGdlbmVyYXRlcyBjb21wcmVoZW5zaXZlIGxlc3NvbiBjb250ZW50XCIsXG4gICAgZmFpbGVkVG9HZW5lcmF0ZUxlc3NvbjogXCJGYWlsZWQgdG8gZ2VuZXJhdGUgbGVzc29uLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuXG4gICAgLy8gSGlzdG9yeSBQYWdlXG4gICAgbm9IaXN0b3J5WWV0OiBcIk5vIEhpc3RvcnkgWWV0XCIsXG5cbiAgICAvLyBRdWVzdGlvbiBDYXJkXG4gICAgcHJvY2Vzc2luZ0Fuc3dlcjogXCJQcm9jZXNzaW5nIHlvdXIgYW5zd2VyLi4uXCIsXG4gICAgY2hvb3NlQW5zd2VyOiBcIkNob29zZSB5b3VyIGFuc3dlclwiXG4gIH0sXG4gIFxuICB2aToge1xuICAgIC8vTG9naW5cbiAgICB3ZWxjb21lTWVzc2FnZTogXCLEkMSDbmcgbmjhuq1wIMSR4buDIHRoZW8gZMO1aSB0aeG6v24gxJHhu5kgdsOgIHRp4bq/cCB04bulYyBo4buNY1wiLFxuICAgIC8vIEFwcCBUaXRsZVxuICAgIGFwcFRpdGxlOiBcIkluZmluaXRlIEVuZ2xpc2hcIixcbiAgICBhcHBTdWJ0aXRsZTogXCJI4buNYyB0aeG6v25nIEFuaCB24bubaSBiw6BpIGjhu41jIHbDoCBxdWl6IEFJXCIsXG4gICAgXG4gICAgLy8gTGV2ZWwgU2VsZWN0aW9uXG4gICAgYmVnaW5uZXI6IFwiQ8ahIGLhuqNuXCIsXG4gICAgaW50ZXJtZWRpYXRlOiBcIlRydW5nIGLDrG5oXCIsXG4gICAgYWR2YW5jZWQ6IFwiTsOibmcgY2FvXCIsIFxuICAgIGJlZ2lubmVyRGVzYzogXCJU4burIHbhu7FuZyBjxqEgYuG6o24gdsOgIG5n4buvIHBow6FwIMSRxqFuIGdp4bqjblwiLFxuICAgIGludGVybWVkaWF0ZURlc2M6IFwiQ8OidSB2w6AgdOG7qyB24buxbmcgcGjhu6ljIHThuqFwIGjGoW5cIixcbiAgICBhZHZhbmNlZERlc2M6IFwiTmfhu68gcGjDoXAgcGjhu6ljIHThuqFwIHbDoCB04burIHbhu7FuZyBuw6JuZyBjYW9cIixcbiAgICBcbiAgICAvLyBOYXZpZ2F0aW9uXG4gICAgYmFja1RvTGV2ZWxzOiBcIlF1YXkgbOG6oWkgQ+G6pXAgxJHhu5lcIixcbiAgICBuZXh0UXVlc3Rpb246IFwiQ8OidSBo4buPaSB0aeG6v3AgdGhlb1wiLFxuICAgIFxuICAgIC8vIFF1aXpcbiAgICBzY29yZTogXCLEkGnhu4NtXCIsXG4gICAgY29ycmVjdDogXCLEkMO6bmcgcuG7k2khXCIsXG4gICAgaW5jb3JyZWN0OiBcIlNhaSBy4buTaSFcIixcbiAgICBxdWVzdGlvbjogXCJDw6J1IGjhu49pOlwiLFxuICAgIHlvdXJBbnN3ZXI6IFwiQ8OidSB0cuG6oyBs4budaSBj4bunYSBi4bqhbjpcIixcbiAgICBjb3JyZWN0QW5zd2VyOiBcIsSQw6FwIMOhbiDEkcO6bmc6XCIsXG4gICAgZXhwbGFuYXRpb246IFwiR2nhuqNpIHRow61jaDpcIixcbiAgICBhbnN3ZXJzOiBcIkPDonUgdHLhuqMgbOG7nWlcIixcbiAgICB0b3RhbDogXCJU4buVbmdcIixcbiAgICBzZWxlY3RBbnN3ZXI6IFwiQ2jhu41uIGPDonUgdHLhuqMgbOG7nWkgY+G7p2EgYuG6oW5cIixcbiAgICBhbnN3ZXJTZWxlY3RlZDogXCLEkMOjIGNo4buNbiBjw6J1IHRy4bqjIGzhu51pISDEkGFuZyBraeG7g20gdHJhLi4uXCIsXG5cbiAgICAvLyBMb2FkaW5nXG4gICAgZ2VuZXJhdGluZ1F1ZXN0aW9uOiBcIsSQYW5nIHThuqFvIGPDonUgaOG7j2kgY2hvIGLhuqFuLi4uXCIsXG4gICAgY3JlYXRpbmdRdWVzdGlvbjogXCLEkGFuZyB04bqhbyBjw6J1IGjhu49pIGPDoSBuaMOibiBow7NhIGTDoG5oIHJpw6puZyBjaG8gYuG6oW5cIixcbiAgICBuYXZpZ2F0aW5nOiBcIsSQYW5nIGNodXnhu4NuIHRyYW5nLi4uXCIsXG4gICAgbG9hZGluZzogXCLEkGFuZyB04bqjaS4uLlwiLFxuXG4gICAgLy8gU2NvcmVcbiAgICByZXNldFNjb3JlOiBcIsSQ4bq3dCBs4bqhaSDEkWnhu4NtXCIsXG5cbiAgICAvLyBMYW5ndWFnZSBTd2l0Y2hlclxuICAgIGxhbmd1YWdlOiBcIk5nw7RuIG5n4buvXCIsXG4gICAgZW5nbGlzaDogXCJFbmdsaXNoXCIsXG4gICAgdmlldG5hbWVzZTogXCJUaeG6v25nIFZp4buHdFwiLFxuICAgIGNoaW5lc2U6IFwi5Lit5paHXCIsXG5cbiAgICAvLyBGZWF0dXJlcyBTZWN0aW9uXG4gICAgd2h5Q2hvb3NlVGl0bGU6IFwiVOG6oWkgc2FvIGNo4buNbiBJbmZpbml0ZSBFbmdsaXNoP1wiLFxuICAgIHdoeUNob29zZVN1YnRpdGxlOiBcIlRy4bqjaSBuZ2hp4buHbSB0xrDGoW5nIGxhaSBj4bunYSB2aeG7h2MgaOG7jWMgbmfDtG4gbmfhu68gduG7m2kgY8OhYyB0w61uaCBuxINuZyB0acOqbiB0aeG6v25cIixcbiAgICBhaUdlbmVyYXRlZFRpdGxlOiBcIkPDonUgaOG7j2kgdsO0IGjhuqFuXCIsXG4gICAgYWlHZW5lcmF0ZWREZXNjOiBcIkPDonUgaOG7j2kgxJHhu5ljIMSRw6FvIGtow7RuZyBnaeG7m2kgaOG6oW4gduG7m2kgdHLhuqNpIG5naGnhu4dtIGjhu41jIHThuq1wIGPDoSBuaMOibiBow7NhIHBow7kgaOG7o3AgduG7m2kgdHLDrG5oIMSR4buZIGPhu6dhIGLhuqFuXCIsXG4gICAgbXVsdGlMYW5ndWFnZVRpdGxlOiBcIkjhu5cgdHLhu6MgxJFhIG5nw7RuIG5n4buvXCIsXG4gICAgbXVsdGlMYW5ndWFnZURlc2M6IFwiSOG7jWMgYuG6sW5nIHRp4bq/bmcgVmnhu4d0LCB0aeG6v25nIFRydW5nIGhv4bq3YyB0aeG6v25nIEFuaCB24bubaSBraOG6oyBuxINuZyBjaHV54buDbiDEkeG7lWkgbmfDtG4gbmfhu68gbGnhu4FuIG3huqFjaFwiLFxuICAgIHByb2dyZXNzVHJhY2tpbmdUaXRsZTogXCJUaGVvIGTDtWkgdGnhur9uIMSR4buZXCIsXG4gICAgcHJvZ3Jlc3NUcmFja2luZ0Rlc2M6IFwiVGhlbyBkw7VpIGjDoG5oIHRyw6xuaCBo4buNYyB04bqtcCBj4bunYSBi4bqhbiB24bubaSB0aOG7kW5nIGvDqiBjaGkgdGnhur90IHbDoCBwaMOibiB0w61jaCBoaeG7h3Ugc3XhuqV0XCIsXG5cbiAgICAvLyBTdGF0c1xuICAgIHF1ZXN0aW9uc0xhYmVsOiBcIkPDonUgaOG7j2lcIixcbiAgICBsYW5ndWFnZXNMYWJlbDogXCJOZ8O0biBuZ+G7r1wiLFxuICAgIGFpUG93ZXJlZExhYmVsOiBcIkjhu41jIHRow7RuZyBtaW5oXCIsXG5cbiAgICAvLyBBdXRoZW50aWNhdGlvblxuICAgIGxvZ2luOiBcIsSQxINuZyBuaOG6rXBcIixcbiAgICByZWdpc3RlcjogXCLEkMSDbmcga8O9XCIsXG4gICAgbG9nb3V0OiBcIsSQxINuZyB4deG6pXRcIixcbiAgICBlbWFpbDogXCJFbWFpbFwiLFxuICAgIHVzZXJuYW1lOiBcIlTDqm4gbmfGsOG7nWkgZMO5bmdcIixcbiAgICBwYXNzd29yZDogXCJN4bqtdCBraOG6qXVcIixcbiAgICBjb25maXJtUGFzc3dvcmQ6IFwiWMOhYyBuaOG6rW4gbeG6rXQga2jhuql1XCIsXG4gICAgbmVlZEFjY291bnQ6IFwiQ2jGsGEgY8OzIHTDoGkga2hv4bqjbj8gxJDEg25nIGvDvVwiLFxuICAgIGhhdmVBY2NvdW50OiBcIsSQw6MgY8OzIHTDoGkga2hv4bqjbj8gxJDEg25nIG5o4bqtcFwiLFxuICAgIGxvZ2luUmVxdWlyZWQ6IFwiWcOqdSBj4bqndSDEkcSDbmcgbmjhuq1wXCIsXG4gICAgbG9naW5Ub1ZpZXdIaXN0b3J5OiBcIlZ1aSBsw7JuZyDEkcSDbmcgbmjhuq1wIMSR4buDIHhlbSBs4buLY2ggc+G7rSBxdWl6XCIsXG5cbiAgICAvLyBVc2VyIFN0YXRzXG4gICAgc3RhdHM6IFwiVGjhu5FuZyBrw6pcIixcbiAgICB0b3RhbFF1ZXN0aW9uczogXCJU4buVbmcgY8OidSBo4buPaVwiLFxuICAgIGFjY3VyYWN5OiBcIsSQ4buZIGNow61uaCB4w6FjXCIsXG4gICAgc3RyZWFrOiBcIkNodeG7l2kgxJHDum5nXCIsXG4gICAgdmlld0hpc3Rvcnk6IFwiWGVtIGzhu4tjaCBz4butXCIsXG5cbiAgICAvLyBIaXN0b3J5XG4gICAgcXVpekhpc3Rvcnk6IFwiTOG7i2NoIHPhu60gUXVpelwiLFxuICAgIGJhY2tUb1F1aXo6IFwiUXVheSBs4bqhaSBRdWl6XCIsXG4gICAgYWxsTGV2ZWxzOiBcIlThuqV0IGPhuqMgY+G6pXAgxJHhu5lcIixcbiAgICBhbGxSZXN1bHRzOiBcIlThuqV0IGPhuqMga+G6v3QgcXXhuqNcIixcbiAgICByZXN1bHQ6IFwiS+G6v3QgcXXhuqNcIixcbiAgICBub0hpc3Rvcnk6IFwiS2jDtG5nIHTDrG0gdGjhuqV5IGzhu4tjaCBz4butIHF1aXpcIixcbiAgICBwcmV2aW91czogXCJUcsaw4bubY1wiLFxuICAgIG5leHQ6IFwiVGnhur9wXCIsXG5cbiAgICAvLyBBSSBGZWF0dXJlc1xuICAgIGFpTGVzc29uczogXCJCw6BpIGjhu41jIEFJXCIsXG4gICAgYWlSZWFkaW5nOiBcIsSQ4buNYyBoaeG7g3UgQUlcIixcbiAgICBhaVR1dG9yOiBcIkdpYSBzxrAgQUlcIixcbiAgICBjcmVhdGVMZXNzb246IFwiVOG6oW8gYsOgaSBo4buNY1wiLFxuICAgIGdlbmVyYXRlUmVhZGluZzogXCJU4bqhbyBiw6BpIMSR4buNY1wiLFxuICAgIGFza1R1dG9yOiBcIkjhu49pIGdpYSBzxrAgQUlcIixcblxuICAgIC8vIExlc3NvbnNcbiAgICBsZXNzb25UaXRsZTogXCJCw6BpIGjhu41jIHRp4bq/bmcgQW5oIEFJXCIsXG4gICAgbGVzc29uU3VidGl0bGU6IFwiVOG6oW8gYsOgaSBo4buNYyB0aeG6v25nIEFuaCBjw6EgbmjDom4gaMOzYSB24bubaSBBSS4gQ2jhu41uIGNo4bunIMSR4buBLCBj4bqlcCDEkeG7mSB2w6AgbG/huqFpIGLDoGkgaOG7jWMgxJHhu4MgdOG6oW8gbuG7mWkgZHVuZyBo4buNYyB04bqtcCB0b8OgbiBkaeG7h24uXCIsXG4gICAgbGVzc29uVHlwZTogXCJMb+G6oWkgYsOgaSBo4buNY1wiLFxuICAgIGR1cmF0aW9uOiBcIlRo4budaSBsxrDhu6NuZ1wiLFxuICAgIHRvcGljOiBcIkNo4bunIMSR4buBXCIsXG4gICAgdG9waWNQbGFjZWhvbGRlcjogXCJ2w60gZOG7pTogVGjDrCBoaeG7h24gdOG6oWkgxJHGoW4sIFRp4bq/bmcgQW5oIHRoxrDGoW5nIG3huqFpLCBU4burIHbhu7FuZyBkdSBs4buLY2guLi5cIixcbiAgICB0b3BpY1N1Z2dlc3Rpb25zOiBcIkhv4bq3YyBjaOG7jW4gdOG7qyBjw6FjIGNo4bunIMSR4buBIHBo4buVIGJp4bq/bjpcIixcbiAgICBnZW5lcmF0ZUxlc3NvbjogXCJU4bqhbyBiw6BpIGjhu41jXCIsXG4gICAgZ2VuZXJhdGluZzogXCLEkGFuZyB04bqhby4uLlwiLFxuXG4gICAgLy8gUmVhZGluZ1xuICAgIHJlYWRpbmdUaXRsZTogXCJMdXnhu4duIMSR4buNYyBoaeG7g3UgQUlcIixcbiAgICByZWFkaW5nU3VidGl0bGU6IFwiVOG6oW8gxJFv4bqhbiB2xINuIMSR4buNYyBoaeG7g3UgY8OhIG5ow6JuIGjDs2EgduG7m2kgQUkuIENo4buNbiBj4bqlcCDEkeG7mSwgY2jhu6cgxJHhu4EgdsOgIHBob25nIGPDoWNoIMSR4buDIHThuqFvIG7hu5lpIGR1bmcgbHV54buHbiB04bqtcCBraMO0bmcgZ2nhu5tpIGjhuqFuLlwiLFxuICAgIHdvcmRDb3VudDogXCJT4buRIHThu6tcIixcbiAgICBzdHlsZTogXCJQaG9uZyBjw6FjaFwiLFxuICAgIGdlbmVyYXRlUGFzc2FnZTogXCJU4bqhbyDEkW/huqFuIHbEg25cIixcbiAgICB2b2NhYnVsYXJ5OiBcIlThu6sgduG7sW5nXCIsXG4gICAgY29tcHJlaGVuc2lvblF1ZXN0aW9uczogXCJDw6J1IGjhu49pIMSR4buNYyBoaeG7g3VcIixcbiAgICBzdWJtaXRBbnN3ZXJzOiBcIk7hu5lwIGLDoGlcIixcbiAgICB0cnlBZ2FpbjogXCJUaOG7rSBs4bqhaVwiLFxuICAgIGdlbmVyYXRlTmV3OiBcIlThuqFvIMSRb+G6oW4gdsSDbiBt4bubaVwiLFxuXG4gICAgLy8gRGFzaGJvYXJkXG4gICAgZGFzaGJvYXJkVGl0bGU6IFwiQuG6o25nIMSRaeG7gXUga2hp4buDbiBo4buNYyB04bqtcFwiLFxuICAgIGRhc2hib2FyZFN1YnRpdGxlOiBcIlRoZW8gZMO1aSB0aeG6v24gxJHhu5ksIHBow6JuIHTDrWNoIGhp4buHdSBzdeG6pXQgdsOgIG5o4bqtbiBn4bujaSDDvSBjw6EgbmjDom4gaMOzYSDEkeG7gyBj4bqjaSB0aGnhu4duIHRp4bq/bmcgQW5oLlwiLFxuICAgIHF1aXp6ZXNUYWtlbjogXCJCw6BpIHF1aXogxJHDoyBsw6BtXCIsXG4gICAgb3ZlcmFsbFBlcmZvcm1hbmNlOiBcIkhp4buHdSBzdeG6pXQgdOG7lW5nIHRo4buDXCIsXG4gICAgc3RyZWFrRGF5czogXCJDaHXhu5dpIG5nw6B5XCIsXG4gICAgc3R1ZHlUaW1lOiBcIlRo4budaSBnaWFuIGjhu41jXCIsXG4gICAgcHJvZ3Jlc3NCeUxldmVsOiBcIlRp4bq/biDEkeG7mSB0aGVvIGPhuqVwIMSR4buZXCIsXG4gICAgYWlBbmFseXNpczogXCJQaMOibiB0w61jaCBoaeG7h3Ugc3XhuqV0IEFJXCIsXG4gICAgc3RyZW5ndGhzOiBcIsSQaeG7g20gbeG6oW5oXCIsXG4gICAgaW1wcm92ZW1lbnRzOiBcIkPhuqduIGPhuqNpIHRoaeG7h25cIixcbiAgICBuZXh0U3RlcHM6IFwiQsaw4bubYyB0aeG6v3AgdGhlb1wiLFxuICAgIG1vdGl2YXRpb25hbE1lc3NhZ2U6IFwiVGjDtG5nIMSRaeG7h3AgxJHhu5luZyB2acOqblwiLFxuICAgIGNvbnRpbnVlbGVhcm5pbmc6IFwiVGnhur9wIHThu6VjIGjhu41jXCIsXG5cbiAgICAvLyBUdXRvclxuICAgIHR1dG9yVGl0bGU6IFwiR2lhIHPGsCB0aeG6v25nIEFuaCBBSVwiLFxuICAgIHR1dG9yU3VidGl0bGU6IFwiTmjhuq1uIHRy4bujIGdpw7pwIHThu6ljIHRow6wgduG7gSBuZ+G7ryBwaMOhcCwgdOG7qyB24buxbmcsIHBow6F0IMOibSB2w6Agbmhp4buBdSBoxqFuIG7hu69hLiDEkOG6t3QgY8OidSBo4buPaSBi4bqxbmcgdGnhur9uZyBt4bq5IMSR4bq7LlwiLFxuICAgIGFza1F1ZXN0aW9uOiBcIsSQ4bq3dCBjw6J1IGjhu49pLi4uXCIsXG4gICAgc2VuZDogXCJH4butaVwiLFxuICAgIGdyYW1tYXJIZWxwOiBcIlRy4bujIGdpw7pwIG5n4buvIHBow6FwXCIsXG4gICAgdm9jYWJ1bGFyeUhlbHA6IFwiVOG7qyB24buxbmdcIixcbiAgICBwcm9udW5jaWF0aW9uSGVscDogXCJQaMOhdCDDom1cIixcbiAgICB3cml0aW5nSGVscDogXCJUcuG7oyBnacO6cCB2aeG6v3RcIixcbiAgICBjb252ZXJzYXRpb25IZWxwOiBcIkjhu5lpIHRob+G6oWlcIixcbiAgICBjdWx0dXJlSGVscDogXCJWxINuIGjDs2FcIixcblxuICAgIC8vIEZvb3RlclxuICAgIGZvb3RlckRlc2NyaXB0aW9uOiBcIkjhu41jIHRp4bq/bmcgQW5oIG1p4buFbiBwaMOtIHbhu5tpIG7hu4FuIHThuqNuZyBBSSB0aMO0bmcgbWluaCBkw6BuaCBjaG8gbmfGsOG7nWkgVmnhu4d0LiBDw6J1IGjhu49pIHRy4bqvYyBuZ2hp4buHbSB2w7QgaOG6oW4sIGLDoGkgaOG7jWMgY8OzIGPhuqV1IHRyw7pjLCB04burIHbhu7FuZyB2w6AgbeG6uW8gaOG7jWMgaGnhu4d1IHF14bqjLiBQaMO5IGjhu6NwIG3hu41pIHRyw6xuaCDEkeG7mSB04burIGPGoSBi4bqjbiDEkeG6v24gbsOibmcgY2FvLlwiLFxuICAgIGNvbnRhY3RBZHZlcnRpc2luZzogXCJMacOqbiBo4buHIMSR4bq3dCBxdeG6o25nIGPDoW9cIixcbiAgICBxdWlja0xpbmtzOiBcIkxpw6puIGvhur90IG5oYW5oXCIsXG4gICAgYWJvdXQ6IFwiR2nhu5tpIHRoaeG7h3VcIixcbiAgICBjb250YWN0OiBcIkxpw6puIGjhu4dcIixcbiAgICBsZWdhbDogXCJQaMOhcCBsw71cIixcbiAgICB0ZXJtczogXCLEkGnhu4F1IGtob+G6o25cIixcbiAgICBwcml2YWN5OiBcIkNow61uaCBzw6FjaCBi4bqjbyBt4bqtdFwiLFxuICAgIGFsbFJpZ2h0c1Jlc2VydmVkOiBcIlThuqV0IGPhuqMgcXV54buBbiDEkcaw4bujYyBi4bqjbyBsxrB1LlwiLFxuICAgIGRpc2NsYWltZXI6IFwiTWnhu4VuIHRy4burIHRyw6FjaCBuaGnhu4dtXCIsXG4gICAgZGlzY2xhaW1lclRleHQ6IFwiVHJhbmcgd2ViIGPhu6dhIGNow7puZyB0w7RpIGNo4buJIGN1bmcgY+G6pXAgZOG7i2NoIHbhu6UgaOG7jWMgdGnhur9uZyBBbmggb25saW5lIHbhu5tpIG3hu6VjIMSRw61jaCBnaeG6o2kgdHLDrSB2w6AgY2hpYSBz4bq7IG7hu5lpIGR1bmcuIFRvw6BuIGLhu5kgbuG7mWkgZHVuZyBo4buNYyB04bqtcCDEkcaw4bujYyDEkcSDbmcgdOG6o2kgdHLDqm4gdHJhbmcgd2ViIMSRxrDhu6NjIHPGsHUgdOG6p20gdOG7qyBuaGnhu4F1IG5ndeG7k24gdHLDqm4gaW50ZXJuZXQgdsOgIGNow7puZyB0w7RpIGtow7RuZyBjaOG7i3UgdHLDoWNoIG5oaeG7h20gduG7gSBi4bqjbiBxdXnhu4FuIGhv4bq3YyBxdXnhu4FuIHPhu58gaOG7r3UgxJHhu5FpIHbhu5tpIGLhuqV0IGvhu7MgbuG7mWkgZHVuZyBuw6BvLiBO4bq/dSBi4bqhbiBsw6AgY2jhu6cgc+G7nyBo4buvdSBi4bqjbiBxdXnhu4FuIHbDoCBjaG8gcuG6sW5nIG7hu5lpIGR1bmcgdHLDqm4gdHJhbmcgdmkgcGjhuqFtIHF1eeG7gW4gY+G7p2EgYuG6oW4sIHZ1aSBsw7JuZyBsacOqbiBo4buHIHbhu5tpIGNow7puZyB0w7RpIMSR4buDIHRp4bq/biBow6BuaCBn4buhIGLhu48gbuG7mWkgZHVuZyB2aSBwaOG6oW0gbeG7mXQgY8OhY2gga+G7i3AgdGjhu51pLiBOZ2/DoGkgcmEsIGNow7puZyB0w7RpIGtow7RuZyBjaOG7i3UgdHLDoWNoIG5oaeG7h20gduG7gSBjw6FjIG7hu5lpIGR1bmcgcXXhuqNuZyBjw6FvIGhp4buDbiB0aOG7iyB0csOqbiB0cmFuZyB3ZWIsIGJhbyBn4buTbSBuaMawbmcga2jDtG5nIGdp4bubaSBo4bqhbiDhu58gdmnhu4djIHF14bqjbmcgY8OhbyBz4bqjbiBwaOG6qW0gaG/hurdjIGThu4tjaCB24bulIGPhu6dhIGLDqm4gdGjhu6kgYmEuIE5o4buvbmcgcXXhuqNuZyBjw6FvIG7DoHkga2jDtG5nIHBo4bqjbiDDoW5oIHF1YW4gxJFp4buDbSBob+G6t2MgY2FtIGvhur90IGPhu6dhIGNow7puZyB0w7RpLiBOZ8aw4budaSBkw7luZyBj4bqnbiB04buxIGPDom4gbmjhuq9jIHbDoCBjaOG7i3UgdHLDoWNoIG5oaeG7h20ga2hpIHTGsMahbmcgdMOhYyB24bubaSBjw6FjIHF14bqjbmcgY8OhbyDEkcOzLlwiLFxuXG4gICAgLy8gQ29tbW9uXG4gICAgbGV2ZWw6IFwiQ+G6pXAgxJHhu5lcIixcbiAgICBiYWNrOiBcIlF1YXkgbOG6oWlcIixcbiAgICBjb250aW51ZTogXCJUaeG6v3AgdOG7pWNcIixcbiAgICBjb21wbGV0ZTogXCJIb8OgbiB0aMOgbmhcIixcbiAgICBzdGFydDogXCJC4bqvdCDEkeG6p3VcIixcbiAgICBmaW5pc2g6IFwiS+G6v3QgdGjDumNcIixcbiAgICBzYXZlOiBcIkzGsHVcIixcbiAgICBjYW5jZWw6IFwiSOG7p3lcIixcbiAgICBjbG9zZTogXCLEkMOzbmdcIixcbiAgICBzaWduSW46IFwixJDEg25nIG5o4bqtcFwiLFxuICAgIHNpZ25PdXQ6IFwixJDEg25nIHh14bqldFwiLFxuICAgIHNpZ25VcDogXCLEkMSDbmcga8O9XCIsXG5cbiAgICAvLyBBdXRoIE1vZGFsXG4gICAgY29udGludWVXaXRoR29vZ2xlOiBcIlRp4bq/cCB04bulYyB24bubaSBHb29nbGVcIixcbiAgICBjb250aW51ZVdpdGhGYWNlYm9vazogXCJUaeG6v3AgdOG7pWMgduG7m2kgRmFjZWJvb2tcIixcbiAgICBvcHRpb25hbExvZ2luTm90ZTogXCLEkMSDbmcgbmjhuq1wIGzDoCB0w7l5IGNo4buNbi4gQuG6oW4gY8OzIHRo4buDIHRp4bq/cCB04bulYyBtw6Aga2jDtG5nIGPhuqduIHTDoGkga2hv4bqjbiwgbmjGsG5nIHRp4bq/biDEkeG7mSBz4bq9IGtow7RuZyDEkcaw4bujYyBsxrB1LlwiLFxuICAgIGxvZ2luRXJyb3I6IFwixJDEg25nIG5o4bqtcCB24bubaSB7cHJvdmlkZXJ9IHRo4bqldCBi4bqhaS4gVnVpIGzDsm5nIHRo4butIGzhuqFpLlwiLFxuXG4gICAgLy8gTGFuZ3VhZ2UgU3dpdGNoZXJcbiAgICBhY3RpdmU6IFwixJBhbmcgaG/huqF0IMSR4buZbmdcIixcblxuICAgIC8vIFVzZXIgTWVudVxuICAgIGxlYXJuaW5nRGFzaGJvYXJkOiBcIkLhuqNuZyDEkWnhu4F1IGtoaeG7g24gaOG7jWMgdOG6rXBcIixcbiAgICBhY2N1cmFjeVRleHQ6IFwixJHhu5kgY2jDrW5oIHjDoWNcIixcblxuICAgIC8vIEVycm9yIE1lc3NhZ2VzXG4gICAgYWlFcnJvck1lc3NhZ2U6IFwiWGluIGzhu5dpLCB0w7RpIGfhurdwIGzhu5dpIGtoaSB44butIGzDvSBjw6J1IGjhu49pIGPhu6dhIGLhuqFuLiBWdWkgbMOybmcgdGjhu60gbOG6oWkuXCIsXG4gICAgZmFpbGVkVG9HZW5lcmF0ZTogXCJLaMO0bmcgdGjhu4MgdOG6oW8gY8OidSBo4buPaVwiLFxuICAgIHF1ZXN0aW9uR2VuZXJhdGVFcnJvcjogXCJYaW4gbOG7l2ksIGNow7puZyB0w7RpIGtow7RuZyB0aOG7gyB04bqhbyBjw6J1IGjhu49pIHbDoG8gbMO6YyBuw6B5LiBWdWkgbMOybmcgdGjhu60gbOG6oWkgc2F1LlwiLFxuICAgIHRyeUFnYWluTGF0ZXI6IFwiVGjhu60gbOG6oWkgc2F1XCIsXG4gICAgYmFja1RvSG9tZTogXCJW4buBIHRyYW5nIGNo4bunXCIsXG5cbiAgICAvLyBMb2FkaW5nIFN0YXRlc1xuICAgIHByb2Nlc3Npbmc6IFwixJBhbmcgeOG7rSBsw70uLi5cIixcbiAgICBwbGVhc2VXYWl0OiBcIlZ1aSBsw7JuZyDEkeG7o2kuLi5cIixcbiAgICBsb2FkaW5nSGlzdG9yeTogXCLEkGFuZyB04bqjaSBs4buLY2ggc+G7rSBxdWl6IGPhu6dhIGLhuqFuLi4uXCIsXG5cbiAgICAvLyBHZW5lcmFsIEVycm9yIFN0YXRlc1xuICAgIHNvbWV0aGluZ1dlbnRXcm9uZzogXCLEkMOjIHjhuqN5IHJhIGzhu5dpXCIsXG4gICAgZXJyb3JPY2N1cnJlZDogXCLEkMOjIHjhuqN5IHJhIGzhu5dpXCIsXG4gICAgcmV0cnlBY3Rpb246IFwiVGjhu60gbOG6oWlcIixcblxuICAgIC8vIEFkZGl0aW9uYWwgUmVhZGluZyBDb21wb25lbnRcbiAgICBhaUdlbmVyYXRlczogXCJBSSBU4bqhbyBu4buZaSBkdW5nXCIsXG4gICAgYWlHZW5lcmF0ZXNEZXNjOiBcIkFJIGPhu6dhIGNow7puZyB0w7RpIHThuqFvIHJhIMSRb+G6oW4gdsSDbiDEkeG7mWMgxJHDoW8gduG7m2kgY8OhYyBjw6J1IGjhu49pXCIsXG4gICAgZmFpbGVkVG9HZW5lcmF0ZVBhc3NhZ2U6IFwiS2jDtG5nIHRo4buDIHThuqFvIMSRb+G6oW4gdsSDbiDEkeG7jWMgaGnhu4N1LiBWdWkgbMOybmcgdGjhu60gbOG6oWkuXCIsXG5cbiAgICAvLyBBZGRpdGlvbmFsIExlc3NvbnMgQ29tcG9uZW50XG4gICAgYWlDcmVhdGVzOiBcIkFJIFThuqFvIGLDoGkgaOG7jWNcIixcbiAgICBhaUNyZWF0ZXNEZXNjOiBcIkFJIHThuqFvIHJhIG7hu5lpIGR1bmcgYsOgaSBo4buNYyB0b8OgbiBkaeG7h25cIixcbiAgICBmYWlsZWRUb0dlbmVyYXRlTGVzc29uOiBcIktow7RuZyB0aOG7gyB04bqhbyBiw6BpIGjhu41jLiBWdWkgbMOybmcgdGjhu60gbOG6oWkuXCIsXG5cbiAgICAvLyBIaXN0b3J5IFBhZ2VcbiAgICBub0hpc3RvcnlZZXQ6IFwiQ2jGsGEgY8OzIGzhu4tjaCBz4butXCIsXG5cbiAgICAvLyBRdWVzdGlvbiBDYXJkXG4gICAgcHJvY2Vzc2luZ0Fuc3dlcjogXCLEkGFuZyB44butIGzDvSBjw6J1IHRy4bqjIGzhu51pIGPhu6dhIGLhuqFuLi4uXCIsXG4gICAgY2hvb3NlQW5zd2VyOiBcIkNo4buNbiBjw6J1IHRy4bqjIGzhu51pIGPhu6dhIGLhuqFuXCJcbiAgfSxcbiAgXG4gIHpoOiB7XG4gICAgLy9Mb2dpblxuICAgIHdlbGNvbWVNZXNzYWdlOiBcIueZu+W9leS7pei3n+i4quaCqOeahOi/m+W6puW5tue7p+e7reWtpuS5oFwiLFxuICAgIC8vIEFwcCBUaXRsZVxuICAgIGFwcFRpdGxlOiBcIkluZmluaXRlIEVuZ2xpc2hcIixcbiAgICBhcHBTdWJ0aXRsZTogXCLnlKhBSeivvueoi+WSjOa1i+mqjOWtpuiLseivrVwiLFxuICAgIFxuICAgIC8vIExldmVsIFNlbGVjdGlvblxuICAgIGJlZ2lubmVyOiBcIuWInee6p1wiLFxuICAgIGludGVybWVkaWF0ZTogXCLkuK3nuqdcIixcbiAgICBhZHZhbmNlZDogXCLpq5jnuqdcIixcbiAgICBiZWdpbm5lckRlc2M6IFwi5Z+656GA6K+N5rGH5ZKM566A5Y2V6K+t5rOVXCIsXG4gICAgaW50ZXJtZWRpYXRlRGVzYzogXCLmm7TlpI3mnYLnmoTlj6XlrZDlkozor43msYdcIixcbiAgICBhZHZhbmNlZERlc2M6IFwi5aSN5p2C6K+t5rOV5ZKM6auY57qn6K+N5rGHXCIsXG4gICAgXG4gICAgLy8gTmF2aWdhdGlvblxuICAgIGJhY2tUb0xldmVsczogXCLihpAg6L+U5Zue57qn5YirXCIsXG4gICAgbmV4dFF1ZXN0aW9uOiBcIuS4i+S4gOmimFwiLFxuICAgIFxuICAgIC8vIFF1aXpcbiAgICBzY29yZTogXCLliIbmlbBcIixcbiAgICBjb3JyZWN0OiBcIuato+ehru+8gVwiLFxuICAgIGluY29ycmVjdDogXCLplJnor6/vvIFcIixcbiAgICBxdWVzdGlvbjogXCLpl67popjvvJpcIixcbiAgICB5b3VyQW5zd2VyOiBcIuaCqOeahOetlOahiO+8mlwiLFxuICAgIGNvcnJlY3RBbnN3ZXI6IFwi5q2j56Gu562U5qGI77yaXCIsXG4gICAgZXhwbGFuYXRpb246IFwi6Kej6YeK77yaXCIsXG4gICAgYW5zd2VyczogXCLnrZTmoYhcIixcbiAgICB0b3RhbDogXCLmgLvorqFcIixcbiAgICBzZWxlY3RBbnN3ZXI6IFwi6YCJ5oup5oKo55qE562U5qGIXCIsXG4gICAgYW5zd2VyU2VsZWN0ZWQ6IFwi5bey6YCJ5oup562U5qGI77yB5q2j5Zyo5qOA5p+lLi4uXCIsXG5cbiAgICAvLyBMb2FkaW5nXG4gICAgZ2VuZXJhdGluZ1F1ZXN0aW9uOiBcIuato+WcqOS4uuaCqOeUn+aIkOmXrumimC4uLlwiLFxuICAgIGNyZWF0aW5nUXVlc3Rpb246IFwi5q2j5Zyo5Li65oKo5Yib5bu65Liq5oCn5YyW6Zeu6aKYXCIsXG4gICAgbmF2aWdhdGluZzogXCLmraPlnKjlr7zoiKouLi5cIixcbiAgICBsb2FkaW5nOiBcIuWKoOi9veS4rS4uLlwiLFxuXG4gICAgLy8gU2NvcmVcbiAgICByZXNldFNjb3JlOiBcIumHjee9ruWIhuaVsFwiLFxuXG4gICAgLy8gTGFuZ3VhZ2UgU3dpdGNoZXJcbiAgICBsYW5ndWFnZTogXCLor63oqIBcIixcbiAgICBlbmdsaXNoOiBcIkVuZ2xpc2hcIixcbiAgICB2aWV0bmFtZXNlOiBcIlRp4bq/bmcgVmnhu4d0XCIsXG4gICAgY2hpbmVzZTogXCLkuK3mlodcIixcblxuICAgIC8vIEZlYXR1cmVzIFNlY3Rpb25cbiAgICB3aHlDaG9vc2VUaXRsZTogXCLkuLrku4DkuYjpgInmi6kgSW5maW5pdGUgRW5nbGlzaO+8n1wiLFxuICAgIHdoeUNob29zZVN1YnRpdGxlOiBcIuS9k+mqjOaIkeS7rOWwluerr+WKn+iDveW4puadpeeahOivreiogOWtpuS5oOacquadpVwiLFxuICAgIGFpR2VuZXJhdGVkVGl0bGU6IFwi5peg6ZmQ6aKY5bqTXCIsXG4gICAgYWlHZW5lcmF0ZWREZXNjOiBcIuaXoOmZkOeLrOeJuemXrumimO+8jOaPkOS+m+mAguWQiOaCqOawtOW5s+eahOS4quaAp+WMluWtpuS5oOS9k+mqjFwiLFxuICAgIG11bHRpTGFuZ3VhZ2VUaXRsZTogXCLlpJror63oqIDmlK/mjIFcIixcbiAgICBtdWx0aUxhbmd1YWdlRGVzYzogXCLmlK/mjIHotorljZfor63jgIHkuK3mlofmiJboi7Hor63lrabkuaDvvIzor63oqIDliIfmjaLml6DnvJ3ooZTmjqVcIixcbiAgICBwcm9ncmVzc1RyYWNraW5nVGl0bGU6IFwi6L+b5bqm6Lef6LiqXCIsXG4gICAgcHJvZ3Jlc3NUcmFja2luZ0Rlc2M6IFwi6YCa6L+H6K+m57uG57uf6K6h5ZKM5oCn6IO95YiG5p6Q55uR5o6n5oKo55qE5a2m5Lmg5Y6G56iLXCIsXG5cbiAgICAvLyBTdGF0c1xuICAgIHF1ZXN0aW9uc0xhYmVsOiBcIumXrumimFwiLFxuICAgIGxhbmd1YWdlc0xhYmVsOiBcIuivreiogFwiLFxuICAgIGFpUG93ZXJlZExhYmVsOiBcIuaZuuiDveWtpuS5oFwiLFxuXG4gICAgLy8gQXV0aGVudGljYXRpb25cbiAgICBsb2dpbjogXCLnmbvlvZVcIixcbiAgICByZWdpc3RlcjogXCLms6jlhoxcIixcbiAgICBsb2dvdXQ6IFwi6YCA5Ye6XCIsXG4gICAgZW1haWw6IFwi6YKu566xXCIsXG4gICAgdXNlcm5hbWU6IFwi55So5oi35ZCNXCIsXG4gICAgcGFzc3dvcmQ6IFwi5a+G56CBXCIsXG4gICAgY29uZmlybVBhc3N3b3JkOiBcIuehruiupOWvhueggVwiLFxuICAgIG5lZWRBY2NvdW50OiBcIuayoeaciei0puaIt++8n+azqOWGjFwiLFxuICAgIGhhdmVBY2NvdW50OiBcIuW3suaciei0puaIt++8n+eZu+W9lVwiLFxuICAgIGxvZ2luUmVxdWlyZWQ6IFwi6ZyA6KaB55m75b2VXCIsXG4gICAgbG9naW5Ub1ZpZXdIaXN0b3J5OiBcIuivt+eZu+W9leafpeeci+a1i+mqjOWOhuWPslwiLFxuXG4gICAgLy8gVXNlciBTdGF0c1xuICAgIHN0YXRzOiBcIue7n+iuoVwiLFxuICAgIHRvdGFsUXVlc3Rpb25zOiBcIuaAu+mimOaVsFwiLFxuICAgIGFjY3VyYWN5OiBcIuWHhuehrueOh1wiLFxuICAgIHN0cmVhazogXCLov57nu63mraPnoa5cIixcbiAgICB2aWV3SGlzdG9yeTogXCLmn6XnnIvljoblj7JcIixcblxuICAgIC8vIEhpc3RvcnlcbiAgICBxdWl6SGlzdG9yeTogXCLmtYvpqozljoblj7JcIixcbiAgICBiYWNrVG9RdWl6OiBcIui/lOWbnua1i+mqjFwiLFxuICAgIGFsbExldmVsczogXCLmiYDmnInnuqfliKtcIixcbiAgICBhbGxSZXN1bHRzOiBcIuaJgOaciee7k+aenFwiLFxuICAgIHJlc3VsdDogXCLnu5PmnpxcIixcbiAgICBub0hpc3Rvcnk6IFwi5pyq5om+5Yiw5rWL6aqM5Y6G5Y+yXCIsXG4gICAgcHJldmlvdXM6IFwi5LiK5LiA6aG1XCIsXG4gICAgbmV4dDogXCLkuIvkuIDpobVcIixcblxuICAgIC8vIEFJIEZlYXR1cmVzXG4gICAgYWlMZXNzb25zOiBcIkFJ6K++56iLXCIsXG4gICAgYWlSZWFkaW5nOiBcIkFJ6ZiF6K+7XCIsXG4gICAgYWlUdXRvcjogXCJBSeWvvOW4iFwiLFxuICAgIGNyZWF0ZUxlc3NvbjogXCLliJvlu7ror77nqItcIixcbiAgICBnZW5lcmF0ZVJlYWRpbmc6IFwi55Sf5oiQ6ZiF6K+7XCIsXG4gICAgYXNrVHV0b3I6IFwi6K+i6ZeuQUnlr7zluIhcIixcblxuICAgIC8vIExlc3NvbnNcbiAgICBsZXNzb25UaXRsZTogXCJBSeiLseivreivvueoi1wiLFxuICAgIGxlc3NvblN1YnRpdGxlOiBcIuS9v+eUqEFJ5Yib5bu65Liq5oCn5YyW6Iux6K+t6K++56iL44CC6YCJ5oup5oKo55qE5Li76aKY44CB57qn5Yir5ZKM6K++56iL57G75Z6L5p2l55Sf5oiQ5YWo6Z2i55qE5a2m5Lmg5YaF5a6544CCXCIsXG4gICAgbGVzc29uVHlwZTogXCLor77nqIvnsbvlnotcIixcbiAgICBkdXJhdGlvbjogXCLml7bplb9cIixcbiAgICB0b3BpYzogXCLkuLvpophcIixcbiAgICB0b3BpY1BsYWNlaG9sZGVyOiBcIuS+i+Wmgu+8muS4gOiIrOeOsOWcqOaXtuOAgeWVhuWKoeiLseivreOAgeaXhea4uOivjeaxhy4uLlwiLFxuICAgIHRvcGljU3VnZ2VzdGlvbnM6IFwi5oiW5LuO54Ot6Zeo5Li76aKY5Lit6YCJ5oup77yaXCIsXG4gICAgZ2VuZXJhdGVMZXNzb246IFwi55Sf5oiQ6K++56iLXCIsXG4gICAgZ2VuZXJhdGluZzogXCLnlJ/miJDkuK0uLi5cIixcblxuICAgIC8vIFJlYWRpbmdcbiAgICByZWFkaW5nVGl0bGU6IFwiQUnpmIXor7vnu4PkuaBcIixcbiAgICByZWFkaW5nU3VidGl0bGU6IFwi5L2/55SoQUnnlJ/miJDkuKrmgKfljJbpmIXor7vmlofnq6DjgILpgInmi6nmgqjnmoTnuqfliKvjgIHkuLvpopjlkozpo47moLzmnaXliJvlu7rml6DpmZDnu4PkuaDlhoXlrrnjgIJcIixcbiAgICB3b3JkQ291bnQ6IFwi5a2X5pWwXCIsXG4gICAgc3R5bGU6IFwi6aOO5qC8XCIsXG4gICAgZ2VuZXJhdGVQYXNzYWdlOiBcIueUn+aIkOaWh+eroFwiLFxuICAgIHZvY2FidWxhcnk6IFwi6K+N5rGHXCIsXG4gICAgY29tcHJlaGVuc2lvblF1ZXN0aW9uczogXCLnkIbop6Ppl67pophcIixcbiAgICBzdWJtaXRBbnN3ZXJzOiBcIuaPkOS6pOetlOahiFwiLFxuICAgIHRyeUFnYWluOiBcIumHjeivlVwiLFxuICAgIGdlbmVyYXRlTmV3OiBcIueUn+aIkOaWsOaWh+eroFwiLFxuXG4gICAgLy8gRGFzaGJvYXJkXG4gICAgZGFzaGJvYXJkVGl0bGU6IFwi5a2m5Lmg5Luq6KGo5p2/XCIsXG4gICAgZGFzaGJvYXJkU3VidGl0bGU6IFwi6Lef6Liq5oKo55qE6L+b5bqm77yM5YiG5p6Q5oKo55qE6KGo546w77yM5bm26I635b6X5Liq5oCn5YyW5bu66K6u5p2l5o+Q6auY5oKo55qE6Iux6K+t44CCXCIsXG4gICAgcXVpenplc1Rha2VuOiBcIuW3suWujOaIkOa1i+mqjFwiLFxuICAgIG92ZXJhbGxQZXJmb3JtYW5jZTogXCLmlbTkvZPooajnjrBcIixcbiAgICBzdHJlYWtEYXlzOiBcIui/nue7reWkqeaVsFwiLFxuICAgIHN0dWR5VGltZTogXCLlrabkuaDml7bpl7RcIixcbiAgICBwcm9ncmVzc0J5TGV2ZWw6IFwi5oyJ57qn5Yir6L+b5bqmXCIsXG4gICAgYWlBbmFseXNpczogXCJBSeihqOeOsOWIhuaekFwiLFxuICAgIHN0cmVuZ3RoczogXCLkvJjlir9cIixcbiAgICBpbXByb3ZlbWVudHM6IFwi6ZyA6KaB5pS56L+bXCIsXG4gICAgbmV4dFN0ZXBzOiBcIuS4i+S4gOatpVwiLFxuICAgIG1vdGl2YXRpb25hbE1lc3NhZ2U6IFwi5r+A5Yqx5L+h5oGvXCIsXG4gICAgY29udGludWVsZWFybmluZzogXCLnu6fnu63lrabkuaBcIixcblxuICAgIC8vIFR1dG9yXG4gICAgdHV0b3JUaXRsZTogXCJBSeiLseivreWvvOW4iFwiLFxuICAgIHR1dG9yU3VidGl0bGU6IFwi6I635b6X6K+t5rOV44CB6K+N5rGH44CB5Y+R6Z+z562J5pa56Z2i55qE5Y2z5pe25biu5Yqp44CC55So5oKo55qE5q+N6K+t5o+Q6Zeu44CCXCIsXG4gICAgYXNrUXVlc3Rpb246IFwi5o+Q6ZeuLi4uXCIsXG4gICAgc2VuZDogXCLlj5HpgIFcIixcbiAgICBncmFtbWFySGVscDogXCLor63ms5XluK7liqlcIixcbiAgICB2b2NhYnVsYXJ5SGVscDogXCLor43msYdcIixcbiAgICBwcm9udW5jaWF0aW9uSGVscDogXCLlj5Hpn7NcIixcbiAgICB3cml0aW5nSGVscDogXCLlhpnkvZzluK7liqlcIixcbiAgICBjb252ZXJzYXRpb25IZWxwOiBcIuWvueivnVwiLFxuICAgIGN1bHR1cmVIZWxwOiBcIuaWh+WMllwiLFxuXG4gICAgLy8gRm9vdGVyXG4gICAgZm9vdGVyRGVzY3JpcHRpb246IFwi5L2/55So5oiR5Lus5LiT5Li66LaK5Y2X5a2m5Lmg6ICF6K6+6K6h55qEQUnlubPlj7DlhY3otLnlrabkuaDoi7Hor63jgILml6DpmZDpgInmi6npopjjgIHnu5PmnoTljJbor77nqIvjgIHor43msYflkozmnInmlYjnmoTlrabkuaDmioDlt6fjgILpgILlkIjku47ln7rnoYDliLDpq5jnuqfnmoTmiYDmnInmsLTlubPjgIJcIixcbiAgICBjb250YWN0QWR2ZXJ0aXNpbmc6IFwi5bm/5ZGK6IGU57O7XCIsXG4gICAgcXVpY2tMaW5rczogXCLlv6vpgJ/pk77mjqVcIixcbiAgICBhYm91dDogXCLlhbPkuo7miJHku6xcIixcbiAgICBjb250YWN0OiBcIuiBlOezu+aIkeS7rFwiLFxuICAgIGxlZ2FsOiBcIuazleW+i1wiLFxuICAgIHRlcm1zOiBcIuacjeWKoeadoeasvlwiLFxuICAgIHByaXZhY3k6IFwi6ZqQ56eB5pS/562WXCIsXG4gICAgYWxsUmlnaHRzUmVzZXJ2ZWQ6IFwi54mI5p2D5omA5pyJ44CCXCIsXG4gICAgZGlzY2xhaW1lcjogXCLlhY3otKPlo7DmmI5cIixcbiAgICBkaXNjbGFpbWVyVGV4dDogXCLmiJHku6znmoTnvZHnq5nku4Xmj5DkvpvlnKjnur/oi7Hor63lrabkuaDmnI3liqHvvIznlKjkuo7lqLHkuZDlkozlhoXlrrnliIbkuqvnm67nmoTjgILnvZHnq5nkuIrlj5HluIPnmoTmiYDmnInlrabkuaDlhoXlrrnlnYfmnaXoh6rkupLogZTnvZHkuIrnmoTlkITnp43mnaXmupDvvIzmiJHku6zlr7nku7vkvZXlhoXlrrnnmoTniYjmnYPmiJbmiYDmnInmnYPkuI3mib/mi4XotKPku7vjgILlpoLmnpzmgqjmmK/niYjmnYPmiYDmnInogIXlubborqTkuLrnvZHnq5nkuIrnmoTlhoXlrrnkvrXniq/kuobmgqjnmoTmnYPliKnvvIzor7fogZTns7vmiJHku6zlj4rml7bliKDpmaTkvrXmnYPlhoXlrrnjgILmraTlpJbvvIzmiJHku6zlr7nnvZHnq5nkuIrmmL7npLrnmoTlub/lkYrlhoXlrrnkuI3mib/mi4XotKPku7vvvIzljIXmi6zkvYbkuI3pmZDkuo7nrKzkuInmlrnkuqflk4HmiJbmnI3liqHnmoTlub/lkYrjgILov5nkupvlub/lkYrkuI3ku6PooajmiJHku6znmoTop4LngrnmiJbmib/or7rjgILnlKjmiLflnKjkuI7mraTnsbvlub/lkYrkupLliqjml7bpnIDopoHoh6rooYzogIPomZHlubbmib/mi4XotKPku7vjgIJcIixcblxuICAgIC8vIENvbW1vblxuICAgIGxldmVsOiBcIue6p+WIq1wiLFxuICAgIGJhY2s6IFwi6L+U5ZueXCIsXG4gICAgY29udGludWU6IFwi57un57utXCIsXG4gICAgY29tcGxldGU6IFwi5a6M5oiQXCIsXG4gICAgc3RhcnQ6IFwi5byA5aeLXCIsXG4gICAgZmluaXNoOiBcIue7k+adn1wiLFxuICAgIHNhdmU6IFwi5L+d5a2YXCIsXG4gICAgY2FuY2VsOiBcIuWPlua2iFwiLFxuICAgIGNsb3NlOiBcIuWFs+mXrVwiLFxuICAgIHNpZ25JbjogXCLnmbvlvZVcIixcbiAgICBzaWduT3V0OiBcIumAgOWHulwiLFxuICAgIHNpZ25VcDogXCLms6jlhoxcIixcblxuICAgIC8vIEF1dGggTW9kYWxcbiAgICBjb250aW51ZVdpdGhHb29nbGU6IFwi5L2/55SoR29vZ2xl57un57utXCIsXG4gICAgY29udGludWVXaXRoRmFjZWJvb2s6IFwi5L2/55SoRmFjZWJvb2vnu6fnu61cIixcbiAgICBvcHRpb25hbExvZ2luTm90ZTogXCLnmbvlvZXmmK/lj6/pgInnmoTjgILmgqjlj6/ku6XlnKjmsqHmnInotKbmiLfnmoTmg4XlhrXkuIvnu6fnu63vvIzkvYbmgqjnmoTov5vluqbkuI3kvJrooqvkv53lrZjjgIJcIixcbiAgICBsb2dpbkVycm9yOiBcIuS9v+eUqHtwcm92aWRlcn3nmbvlvZXlpLHotKXjgILor7fph43or5XjgIJcIixcblxuICAgIC8vIExhbmd1YWdlIFN3aXRjaGVyXG4gICAgYWN0aXZlOiBcIua0u+i3g1wiLFxuXG4gICAgLy8gVXNlciBNZW51XG4gICAgbGVhcm5pbmdEYXNoYm9hcmQ6IFwi5a2m5Lmg5Luq6KGo5p2/XCIsXG4gICAgYWNjdXJhY3lUZXh0OiBcIuWHhuehrueOh1wiLFxuXG4gICAgLy8gRXJyb3IgTWVzc2FnZXNcbiAgICBhaUVycm9yTWVzc2FnZTogXCLmirHmrYnvvIzlpITnkIbmgqjnmoTpl67popjml7bpgYfliLDplJnor6/jgILor7fph43or5XjgIJcIixcbiAgICBmYWlsZWRUb0dlbmVyYXRlOiBcIueUn+aIkOmXrumimOWksei0pVwiLFxuICAgIHF1ZXN0aW9uR2VuZXJhdGVFcnJvcjogXCLmirHmrYnvvIzmiJHku6znm67liY3ml6Dms5XnlJ/miJDpl67popjjgILor7fnqI3lkI7ph43or5XjgIJcIixcbiAgICB0cnlBZ2FpbkxhdGVyOiBcIueojeWQjumHjeivlVwiLFxuICAgIGJhY2tUb0hvbWU6IFwi6L+U5Zue6aaW6aG1XCIsXG5cbiAgICAvLyBMb2FkaW5nIFN0YXRlc1xuICAgIHByb2Nlc3Npbmc6IFwi5aSE55CG5LitLi4uXCIsXG4gICAgcGxlYXNlV2FpdDogXCLor7fnqI3lgJkuLi5cIixcbiAgICBsb2FkaW5nSGlzdG9yeTogXCLmraPlnKjliqDovb3mgqjnmoTmtYvpqozljoblj7IuLi5cIixcblxuICAgIC8vIEdlbmVyYWwgRXJyb3IgU3RhdGVzXG4gICAgc29tZXRoaW5nV2VudFdyb25nOiBcIuWHuueOsOmXrumimFwiLFxuICAgIGVycm9yT2NjdXJyZWQ6IFwi5Y+R55Sf6ZSZ6K+vXCIsXG4gICAgcmV0cnlBY3Rpb246IFwi6YeN6K+VXCIsXG5cbiAgICAvLyBBZGRpdGlvbmFsIFJlYWRpbmcgQ29tcG9uZW50XG4gICAgYWlHZW5lcmF0ZXM6IFwiQUnnlJ/miJBcIixcbiAgICBhaUdlbmVyYXRlc0Rlc2M6IFwi5oiR5Lus55qEQUnliJvlu7rni6znibnnmoTmlofnq6Dlkozpl67pophcIixcbiAgICBmYWlsZWRUb0dlbmVyYXRlUGFzc2FnZTogXCLnlJ/miJDpmIXor7vmlofnq6DlpLHotKXjgILor7fph43or5XjgIJcIixcblxuICAgIC8vIEFkZGl0aW9uYWwgTGVzc29ucyBDb21wb25lbnRcbiAgICBhaUNyZWF0ZXM6IFwiQUnliJvlu7pcIixcbiAgICBhaUNyZWF0ZXNEZXNjOiBcIkFJ55Sf5oiQ5YWo6Z2i55qE6K++56iL5YaF5a65XCIsXG4gICAgZmFpbGVkVG9HZW5lcmF0ZUxlc3NvbjogXCLnlJ/miJDor77nqIvlpLHotKXjgILor7fph43or5XjgIJcIixcblxuICAgIC8vIEhpc3RvcnkgUGFnZVxuICAgIG5vSGlzdG9yeVlldDogXCLmmoLml6Dljoblj7LorrDlvZVcIixcblxuICAgIC8vIFF1ZXN0aW9uIENhcmRcbiAgICBwcm9jZXNzaW5nQW5zd2VyOiBcIuato+WcqOWkhOeQhuaCqOeahOetlOahiC4uLlwiLFxuICAgIGNob29zZUFuc3dlcjogXCLpgInmi6nmgqjnmoTnrZTmoYhcIlxuICB9XG59O1xuIl0sIm5hbWVzIjpbInRyYW5zbGF0aW9ucyIsImVuIiwid2VsY29tZU1lc3NhZ2UiLCJhcHBUaXRsZSIsImFwcFN1YnRpdGxlIiwiYmVnaW5uZXIiLCJpbnRlcm1lZGlhdGUiLCJhZHZhbmNlZCIsImJlZ2lubmVyRGVzYyIsImludGVybWVkaWF0ZURlc2MiLCJhZHZhbmNlZERlc2MiLCJiYWNrVG9MZXZlbHMiLCJuZXh0UXVlc3Rpb24iLCJzY29yZSIsImNvcnJlY3QiLCJpbmNvcnJlY3QiLCJxdWVzdGlvbiIsInlvdXJBbnN3ZXIiLCJjb3JyZWN0QW5zd2VyIiwiZXhwbGFuYXRpb24iLCJhbnN3ZXJzIiwidG90YWwiLCJzZWxlY3RBbnN3ZXIiLCJhbnN3ZXJTZWxlY3RlZCIsImdlbmVyYXRpbmdRdWVzdGlvbiIsImNyZWF0aW5nUXVlc3Rpb24iLCJuYXZpZ2F0aW5nIiwibG9hZGluZyIsInJlc2V0U2NvcmUiLCJsYW5ndWFnZSIsImVuZ2xpc2giLCJ2aWV0bmFtZXNlIiwiY2hpbmVzZSIsIndoeUNob29zZVRpdGxlIiwid2h5Q2hvb3NlU3VidGl0bGUiLCJhaUdlbmVyYXRlZFRpdGxlIiwiYWlHZW5lcmF0ZWREZXNjIiwibXVsdGlMYW5ndWFnZVRpdGxlIiwibXVsdGlMYW5ndWFnZURlc2MiLCJwcm9ncmVzc1RyYWNraW5nVGl0bGUiLCJwcm9ncmVzc1RyYWNraW5nRGVzYyIsInF1ZXN0aW9uc0xhYmVsIiwibGFuZ3VhZ2VzTGFiZWwiLCJhaVBvd2VyZWRMYWJlbCIsImxvZ2luIiwicmVnaXN0ZXIiLCJsb2dvdXQiLCJlbWFpbCIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJjb25maXJtUGFzc3dvcmQiLCJuZWVkQWNjb3VudCIsImhhdmVBY2NvdW50IiwibG9naW5SZXF1aXJlZCIsImxvZ2luVG9WaWV3SGlzdG9yeSIsInN0YXRzIiwidG90YWxRdWVzdGlvbnMiLCJhY2N1cmFjeSIsInN0cmVhayIsInZpZXdIaXN0b3J5IiwicXVpekhpc3RvcnkiLCJiYWNrVG9RdWl6IiwiYWxsTGV2ZWxzIiwiYWxsUmVzdWx0cyIsInJlc3VsdCIsIm5vSGlzdG9yeSIsInByZXZpb3VzIiwibmV4dCIsImFpTGVzc29ucyIsImFpUmVhZGluZyIsImFpVHV0b3IiLCJjcmVhdGVMZXNzb24iLCJnZW5lcmF0ZVJlYWRpbmciLCJhc2tUdXRvciIsImxlc3NvblRpdGxlIiwibGVzc29uU3VidGl0bGUiLCJsZXNzb25UeXBlIiwiZHVyYXRpb24iLCJ0b3BpYyIsInRvcGljUGxhY2Vob2xkZXIiLCJ0b3BpY1N1Z2dlc3Rpb25zIiwiZ2VuZXJhdGVMZXNzb24iLCJnZW5lcmF0aW5nIiwicmVhZGluZ1RpdGxlIiwicmVhZGluZ1N1YnRpdGxlIiwid29yZENvdW50Iiwic3R5bGUiLCJnZW5lcmF0ZVBhc3NhZ2UiLCJ2b2NhYnVsYXJ5IiwiY29tcHJlaGVuc2lvblF1ZXN0aW9ucyIsInN1Ym1pdEFuc3dlcnMiLCJ0cnlBZ2FpbiIsImdlbmVyYXRlTmV3IiwiZGFzaGJvYXJkVGl0bGUiLCJkYXNoYm9hcmRTdWJ0aXRsZSIsInF1aXp6ZXNUYWtlbiIsIm92ZXJhbGxQZXJmb3JtYW5jZSIsInN0cmVha0RheXMiLCJzdHVkeVRpbWUiLCJwcm9ncmVzc0J5TGV2ZWwiLCJhaUFuYWx5c2lzIiwic3RyZW5ndGhzIiwiaW1wcm92ZW1lbnRzIiwibmV4dFN0ZXBzIiwibW90aXZhdGlvbmFsTWVzc2FnZSIsImNvbnRpbnVlbGVhcm5pbmciLCJ0dXRvclRpdGxlIiwidHV0b3JTdWJ0aXRsZSIsImFza1F1ZXN0aW9uIiwic2VuZCIsImdyYW1tYXJIZWxwIiwidm9jYWJ1bGFyeUhlbHAiLCJwcm9udW5jaWF0aW9uSGVscCIsIndyaXRpbmdIZWxwIiwiY29udmVyc2F0aW9uSGVscCIsImN1bHR1cmVIZWxwIiwiZm9vdGVyRGVzY3JpcHRpb24iLCJjb250YWN0QWR2ZXJ0aXNpbmciLCJxdWlja0xpbmtzIiwiYWJvdXQiLCJjb250YWN0IiwibGVnYWwiLCJ0ZXJtcyIsInByaXZhY3kiLCJhbGxSaWdodHNSZXNlcnZlZCIsImRpc2NsYWltZXIiLCJkaXNjbGFpbWVyVGV4dCIsImxldmVsIiwiYmFjayIsImNvbnRpbnVlIiwiY29tcGxldGUiLCJzdGFydCIsImZpbmlzaCIsInNhdmUiLCJjYW5jZWwiLCJjbG9zZSIsInNpZ25JbiIsInNpZ25PdXQiLCJzaWduVXAiLCJjb250aW51ZVdpdGhHb29nbGUiLCJjb250aW51ZVdpdGhGYWNlYm9vayIsIm9wdGlvbmFsTG9naW5Ob3RlIiwibG9naW5FcnJvciIsImFjdGl2ZSIsImxlYXJuaW5nRGFzaGJvYXJkIiwiYWNjdXJhY3lUZXh0IiwiYWlFcnJvck1lc3NhZ2UiLCJmYWlsZWRUb0dlbmVyYXRlIiwicXVlc3Rpb25HZW5lcmF0ZUVycm9yIiwidHJ5QWdhaW5MYXRlciIsImJhY2tUb0hvbWUiLCJwcm9jZXNzaW5nIiwicGxlYXNlV2FpdCIsImxvYWRpbmdIaXN0b3J5Iiwic29tZXRoaW5nV2VudFdyb25nIiwiZXJyb3JPY2N1cnJlZCIsInJldHJ5QWN0aW9uIiwiYWlHZW5lcmF0ZXMiLCJhaUdlbmVyYXRlc0Rlc2MiLCJmYWlsZWRUb0dlbmVyYXRlUGFzc2FnZSIsImFpQ3JlYXRlcyIsImFpQ3JlYXRlc0Rlc2MiLCJmYWlsZWRUb0dlbmVyYXRlTGVzc29uIiwibm9IaXN0b3J5WWV0IiwicHJvY2Vzc2luZ0Fuc3dlciIsImNob29zZUFuc3dlciIsInZpIiwiemgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/i18n/translations.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvocabulary%2Fpage&page=%2Fvocabulary%2Fpage&appPaths=%2Fvocabulary%2Fpage&pagePath=private-next-app-dir%2Fvocabulary%2Fpage.tsx&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
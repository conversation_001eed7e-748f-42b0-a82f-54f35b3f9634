'use client';

import { useState, useEffect, useRef } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDropdown } from '@/contexts/DropdownContext';
import { Language } from '@/types';

interface LanguageSwitcherProps {
  isScrolled?: boolean;
}

export default function LanguageSwitcher({ isScrolled = false }: LanguageSwitcherProps) {
  const { language, setLanguage, t } = useLanguage();
  const { openDropdown, setOpenDropdown } = useDropdown();
  const [isChanging, setIsChanging] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const isOpen = openDropdown === 'language';

  const languages = [
    { code: 'vi' as Language, name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' },
    { code: 'zh' as Language, name: '中文', flag: '🇨🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, setOpenDropdown]);

  const handleLanguageChange = async (newLanguage: Language) => {
    if (newLanguage === language) {
      setOpenDropdown(null);
      return;
    }

    setIsChanging(true);
    setOpenDropdown(null);

    // Add a small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 300));

    setLanguage(newLanguage);
    setIsChanging(false);
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpenDropdown(isOpen ? null : 'language');
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        disabled={isChanging}
        type="button"
        className={`group flex items-center space-x-3 rounded-xl px-4 py-2.5 shadow-sm hover:shadow-md transition-all duration-500 backdrop-blur-sm disabled:opacity-75 disabled:cursor-not-allowed ${
          isScrolled
            ? 'bg-white/80 hover:bg-white/90'
            : 'bg-white/80 hover:bg-white/90'
        }`}
      >
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200 ${
          isScrolled
            ? 'bg-gradient-to-br from-gray-100 to-gray-200'
            : 'bg-gradient-to-br from-gray-100 to-gray-200'
        }`}>
          {isChanging ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
          ) : (
            <span className="text-lg">{currentLanguage?.flag}</span>
          )}
        </div>
        <span className={`text-sm font-semibold hidden sm:block transition-colors duration-500 ${
          isScrolled ? 'text-gray-700' : 'text-gray-700'
        }`}>
          {currentLanguage?.name}
        </span>
        <svg
          className={`w-4 h-4 transition-all duration-500 ${
            isOpen
              ? `rotate-180 ${isScrolled ? 'text-gray-700' : 'text-gray-700'}`
              : `${isScrolled ? 'text-gray-500 group-hover:text-gray-700' : 'text-gray-500 group-hover:text-gray-700'}`
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-[9998]"
            onClick={() => setOpenDropdown(null)}
          />

          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-2xl shadow-xl z-[9999] overflow-hidden">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleLanguageChange(lang.code);
                }}
                type="button"
                className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150 ${
                  language === lang.code
                    ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                    : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                  language === lang.code
                    ? 'bg-blue-100'
                    : 'bg-gray-100'
                }`}>
                  <span className="text-lg">{lang.flag}</span>
                </div>
                <div className="flex-1">
                  <span className="text-sm font-semibold">{lang.name}</span>
                  {language === lang.code && (
                    <div className="flex items-center mt-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-xs text-blue-600">{t('active')}</span>
                    </div>
                  )}
                </div>
                {language === lang.code && (
                  <div className="flex-shrink-0">
                    <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}

import { Metadata } from 'next';
import DashboardClient from '@/components/DashboardClient';

export const metadata: Metadata = {
  title: 'Learning Dashboard - Infinite English',
  description: 'Track your English learning progress with detailed analytics, personalized recommendations, and study insights.',
  keywords: 'learning dashboard, progress tracking, English learning analytics, study statistics, personalized recommendations',
  openGraph: {
    title: 'Learning Dashboard - Infinite English',
    description: 'Track your English learning progress with detailed analytics and personalized recommendations.',
    type: 'website',
  },
};

export default function DashboardPage() {
  return <DashboardClient />;
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, Level } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import AdSense from '@/components/AdSense';
import { VocabularyLevelStructuredData } from '@/components/StructuredData';

interface Props {
  level: Level;
  initialTopics: VocabularyTopic[];
}

export default function VocabularyLevelServerPage({ level, initialTopics }: Props) {
  const router = useRouter();
  const { language } = useLanguage();
  const [topics] = useState<VocabularyTopic[]>(initialTopics);
  const [loading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Note: Language changes are handled by the language context
  // which will trigger a page refresh automatically

  const handleTopicClick = (slug: string) => {
    router.push(`/vocabulary/${slug}`);
  };

  const getLevelText = () => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getPageTitle = () => {
    const levelText = getLevelText();
    switch (language) {
      case 'vi':
        return `Từ Vựng ${levelText}`;
      case 'zh':
        return `${levelText}词汇`;
      default:
        return `${levelText} Vocabulary`;
    }
  };

  const getPageDescription = () => {
    const levelText = getLevelText().toLowerCase();
    switch (language) {
      case 'vi':
        return `Khám phá từ vựng tiếng Anh cấp độ ${levelText}. Học từ mới với định nghĩa, ví dụ và hướng dẫn phát âm phù hợp với trình độ của bạn.`;
      case 'zh':
        return `探索${levelText}级英语词汇。通过定义、例句和发音指导学习适合您水平的新单词。`;
      default:
        return `Explore ${levelText}-level English vocabulary. Learn new words with definitions, examples, and pronunciation guides suitable for your level.`;
    }
  };

  const getBreadcrumbText = () => {
    switch (language) {
      case 'vi':
        return 'Từ vựng';
      case 'zh':
        return '词汇';
      default:
        return 'Vocabulary';
    }
  };

  if (loading && topics.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <>
      <VocabularyLevelStructuredData level={level} topics={topics} language={language} />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <ol className="flex items-center space-x-2 text-sm text-gray-600">
              <li>
                <button
                  onClick={() => router.push('/vocabulary')}
                  className="hover:text-blue-600 transition-colors duration-200"
                >
                  {getBreadcrumbText()}
                </button>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium">{getLevelText()}</li>
            </ol>
          </nav>

          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {getPageTitle()}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense adSlot="vocabulary-level" />
          </div>

          {/* Loading overlay for language changes */}
          {loading && (
            <div className="relative">
              <div className="absolute inset-0 bg-white bg-opacity-75 z-10 flex items-center justify-center">
                <LoadingSpinner />
              </div>
            </div>
          )}

          {/* Topics Grid */}
          {topics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {topics.map((topic) => (
                <div
                  key={topic._id}
                  onClick={() => handleTopicClick(topic.slug)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                      style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                    >
                      {topic.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900 mb-1">
                        {topic.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          topic.level === 'beginner' ? 'bg-green-100 text-green-700' :
                          topic.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {getLevelText()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {topic.wordCount} {language === 'vi' ? 'từ' : language === 'zh' ? '词' : 'words'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm line-clamp-2">
                    {topic.description}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {language === 'vi' ? `Không tìm thấy chủ đề ${getLevelText().toLowerCase()} nào` : 
                 language === 'zh' ? `未找到任何${getLevelText()}主题` : 
                 `No ${getLevelText().toLowerCase()} topics found`}
              </h3>
              <p className="text-gray-500">
                {language === 'vi' ? 'Chúng tôi đang cập nhật thêm nội dung. Vui lòng quay lại sau.' : 
                 language === 'zh' ? '我们正在更新更多内容。请稍后再试。' : 
                 'We are updating more content. Please come back later.'}
              </p>
            </div>
          )}
        </div>
      </div>

      <Footer />
      
        {showAuthModal && (
          <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
        )}
      </div>
    </>
  );
}

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Define schemas directly in the script
const VocabularyTopicSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  slug: { type: String, required: true, unique: true, lowercase: true, trim: true },
  description: { type: String, required: true, trim: true },
  icon: { type: String, required: true, default: '📚' },
  color: { type: String, required: true, default: '#3B82F6' },
  level: { type: String, enum: ['beginner', 'intermediate', 'advanced'], required: true },
  language: { type: String, enum: ['en', 'vi', 'zh'], required: true, default: 'en' },
  wordCount: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true },
  order: { type: Number, default: 0 },
  seoTitle: { type: String, trim: true },
  seoDescription: { type: String, trim: true },
  seoKeywords: [{ type: String, trim: true }],
}, { timestamps: true });

const VocabularyWordSchema = new mongoose.Schema({
  topicId: { type: mongoose.Schema.Types.ObjectId, ref: 'VocabularyTopic', required: true },
  word: { type: String, required: true, trim: true, lowercase: true },
  pronunciation: { type: String, required: true, trim: true },
  phonetic: { type: String, required: true, trim: true },
  partOfSpeech: { type: String, required: true, enum: ['noun', 'verb', 'adjective', 'adverb', 'preposition', 'conjunction', 'interjection', 'pronoun'] },
  level: { type: String, enum: ['beginner', 'intermediate', 'advanced'], required: true },
  language: { type: String, enum: ['en', 'vi', 'zh'], required: true, default: 'en' },
  definitions: [{
    meaning: { type: String, required: true, trim: true },
    example: { type: String, required: true, trim: true },
    translation: { type: String, trim: true },
  }],
  synonyms: [{ type: String, trim: true }],
  antonyms: [{ type: String, trim: true }],
  collocations: [{ type: String, trim: true }],
  frequency: { type: Number, min: 1, max: 5, default: 3 },
  difficulty: { type: Number, min: 1, max: 5, default: 3 },
  audioUrl: { type: String, trim: true },
  imageUrl: { type: String, trim: true },
  tags: [{ type: String, trim: true }],
  isActive: { type: Boolean, default: true },
  order: { type: Number, default: 0 },
}, { timestamps: true });

// Create models
const VocabularyTopic = mongoose.model('VocabularyTopic', VocabularyTopicSchema);
const VocabularyWord = mongoose.model('VocabularyWord', VocabularyWordSchema);

// Sample vocabulary data
const vocabularyData = {
  topics: [
    {
      name: 'Business & Work',
      slug: 'business-work',
      description: 'Essential vocabulary for business communication and workplace interactions',
      icon: '💼',
      color: '#3B82F6',
      level: 'intermediate',
      language: 'en',
      order: 1,
      seoTitle: 'Business English Vocabulary - Infinite English',
      seoDescription: 'Learn essential business English vocabulary for workplace communication, meetings, and professional interactions.',
      seoKeywords: ['business english', 'workplace vocabulary', 'professional english', 'business communication']
    },
    {
      name: 'Travel & Tourism',
      slug: 'travel-tourism',
      description: 'Vocabulary for traveling, booking hotels, and exploring new places',
      icon: '✈️',
      color: '#10B981',
      level: 'beginner',
      language: 'en',
      order: 2,
      seoTitle: 'Travel English Vocabulary - Infinite English',
      seoDescription: 'Master travel English vocabulary for airports, hotels, restaurants, and tourist attractions.',
      seoKeywords: ['travel english', 'tourism vocabulary', 'airport english', 'hotel english']
    },
    {
      name: 'Food & Cooking',
      slug: 'food-cooking',
      description: 'Culinary vocabulary for cooking, dining, and food-related conversations',
      icon: '🍳',
      color: '#F59E0B',
      level: 'beginner',
      language: 'en',
      order: 3,
      seoTitle: 'Food & Cooking Vocabulary - Infinite English',
      seoDescription: 'Learn food and cooking vocabulary in English. Perfect for restaurant visits and culinary conversations.',
      seoKeywords: ['food vocabulary', 'cooking english', 'restaurant english', 'culinary terms']
    },
    {
      name: 'Technology',
      slug: 'technology',
      description: 'Modern technology vocabulary for digital age communication',
      icon: '💻',
      color: '#8B5CF6',
      level: 'intermediate',
      language: 'en',
      order: 4,
      seoTitle: 'Technology Vocabulary - Infinite English',
      seoDescription: 'Stay updated with technology vocabulary in English. Learn IT, software, and digital terms.',
      seoKeywords: ['technology vocabulary', 'IT english', 'computer terms', 'digital vocabulary']
    },
    {
      name: 'Health & Medicine',
      slug: 'health-medicine',
      description: 'Medical and health-related vocabulary for healthcare conversations',
      icon: '🏥',
      color: '#EF4444',
      level: 'advanced',
      language: 'en',
      order: 5,
      seoTitle: 'Health & Medical Vocabulary - Infinite English',
      seoDescription: 'Learn essential health and medical vocabulary in English for doctor visits and health discussions.',
      seoKeywords: ['medical vocabulary', 'health english', 'doctor english', 'medical terms']
    },
    {
      name: 'Family & Relationships',
      slug: 'family-relationships',
      description: 'Vocabulary for talking about family members and personal relationships',
      icon: '👨‍👩‍👧‍👦',
      color: '#F97316',
      level: 'beginner',
      language: 'en',
      order: 6,
      seoTitle: 'Family & Relationships Vocabulary - Infinite English',
      seoDescription: 'Learn family and relationship vocabulary in English. Perfect for describing family members and relationships.',
      seoKeywords: ['family vocabulary', 'relationship english', 'family members', 'personal relationships']
    },
    {
      name: 'Weather & Climate',
      slug: 'weather-climate',
      description: 'Weather conditions, climate, and seasonal vocabulary',
      icon: '🌤️',
      color: '#06B6D4',
      level: 'beginner',
      language: 'en',
      order: 7,
      seoTitle: 'Weather & Climate Vocabulary - Infinite English',
      seoDescription: 'Master weather and climate vocabulary in English. Learn to describe weather conditions and seasons.',
      seoKeywords: ['weather vocabulary', 'climate english', 'seasons vocabulary', 'weather conditions']
    },
    {
      name: 'Education & School',
      slug: 'education-school',
      description: 'Academic vocabulary for students and educational contexts',
      icon: '🎓',
      color: '#7C3AED',
      level: 'intermediate',
      language: 'en',
      order: 8,
      seoTitle: 'Education & School Vocabulary - Infinite English',
      seoDescription: 'Learn education and school vocabulary in English. Essential for students and academic discussions.',
      seoKeywords: ['education vocabulary', 'school english', 'academic terms', 'student vocabulary']
    },
    {
      name: 'Sports & Fitness',
      slug: 'sports-fitness',
      description: 'Sports, exercise, and fitness-related vocabulary',
      icon: '⚽',
      color: '#059669',
      level: 'intermediate',
      language: 'en',
      order: 9,
      seoTitle: 'Sports & Fitness Vocabulary - Infinite English',
      seoDescription: 'Learn sports and fitness vocabulary in English. Perfect for talking about exercise and sports activities.',
      seoKeywords: ['sports vocabulary', 'fitness english', 'exercise terms', 'sports activities']
    },
    {
      name: 'Shopping & Money',
      slug: 'shopping-money',
      description: 'Shopping, money, and financial vocabulary for everyday transactions',
      icon: '🛒',
      color: '#DC2626',
      level: 'beginner',
      language: 'en',
      order: 10,
      seoTitle: 'Shopping & Money Vocabulary - Infinite English',
      seoDescription: 'Learn shopping and money vocabulary in English. Essential for shopping and financial conversations.',
      seoKeywords: ['shopping vocabulary', 'money english', 'financial terms', 'shopping expressions']
    }
  ],
  
  words: {
    'business-work': [
      {
        word: 'meeting',
        pronunciation: '/ˈmiːtɪŋ/',
        phonetic: 'MEE-ting',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'A gathering of people for discussion or decision-making',
            example: 'We have a team meeting every Monday morning.',
            translation: 'Cuộc họp'
          }
        ],
        synonyms: ['conference', 'gathering', 'assembly'],
        antonyms: [],
        collocations: ['attend a meeting', 'schedule a meeting', 'meeting room'],
        frequency: 5,
        difficulty: 2,
        tags: ['business', 'communication']
      },
      {
        word: 'deadline',
        pronunciation: '/ˈdedlaɪn/',
        phonetic: 'DED-line',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'The latest time or date by which something should be completed',
            example: 'The deadline for the project is next Friday.',
            translation: 'Hạn chót'
          }
        ],
        synonyms: ['due date', 'time limit', 'cutoff'],
        antonyms: [],
        collocations: ['meet the deadline', 'extend deadline', 'tight deadline'],
        frequency: 4,
        difficulty: 3,
        tags: ['business', 'time management']
      },
      {
        word: 'presentation',
        pronunciation: '/ˌprezənˈteɪʃən/',
        phonetic: 'prez-uhn-TAY-shuhn',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'A formal talk giving information about something',
            example: 'She gave an excellent presentation about our new product.',
            translation: 'Bài thuyết trình'
          }
        ],
        synonyms: ['speech', 'talk', 'demonstration'],
        antonyms: [],
        collocations: ['give a presentation', 'PowerPoint presentation', 'presentation skills'],
        frequency: 4,
        difficulty: 3,
        tags: ['business', 'communication']
      }
    ],
    
    'travel-tourism': [
      {
        word: 'passport',
        pronunciation: '/ˈpæspɔːrt/',
        phonetic: 'PASS-port',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'An official document that allows you to travel to other countries',
            example: 'Don\'t forget to bring your passport to the airport.',
            translation: 'Hộ chiếu'
          }
        ],
        synonyms: ['travel document', 'ID'],
        antonyms: [],
        collocations: ['passport control', 'valid passport', 'passport photo'],
        frequency: 4,
        difficulty: 1,
        tags: ['travel', 'documents']
      },
      {
        word: 'luggage',
        pronunciation: '/ˈlʌɡɪdʒ/',
        phonetic: 'LUG-ij',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'Bags and suitcases used for traveling',
            example: 'Please put your luggage in the overhead compartment.',
            translation: 'Hành lý'
          }
        ],
        synonyms: ['baggage', 'suitcases', 'bags'],
        antonyms: [],
        collocations: ['carry-on luggage', 'check luggage', 'luggage claim'],
        frequency: 4,
        difficulty: 2,
        tags: ['travel', 'airport']
      }
    ],
    
    'food-cooking': [
      {
        word: 'recipe',
        pronunciation: '/ˈresəpi/',
        phonetic: 'RES-uh-pee',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'A set of instructions for preparing a dish',
            example: 'I found a great recipe for chocolate cake online.',
            translation: 'Công thức nấu ăn'
          }
        ],
        synonyms: ['formula', 'instructions', 'method'],
        antonyms: [],
        collocations: ['follow a recipe', 'family recipe', 'recipe book'],
        frequency: 3,
        difficulty: 2,
        tags: ['cooking', 'food preparation']
      },
      {
        word: 'ingredient',
        pronunciation: '/ɪnˈɡriːdiənt/',
        phonetic: 'in-GREE-dee-uhnt',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'Any of the foods or substances that are combined to make a particular dish',
            example: 'The main ingredients for this soup are tomatoes and onions.',
            translation: 'Nguyên liệu'
          }
        ],
        synonyms: ['component', 'element', 'constituent'],
        antonyms: [],
        collocations: ['fresh ingredients', 'main ingredient', 'secret ingredient'],
        frequency: 3,
        difficulty: 3,
        tags: ['cooking', 'food preparation']
      }
    ],

    'technology': [
      {
        word: 'software',
        pronunciation: '/ˈsɔːftwer/',
        phonetic: 'SAWFT-wair',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'Computer programs and applications',
            example: 'We need to update our software to the latest version.',
            translation: 'Phần mềm'
          }
        ],
        synonyms: ['program', 'application', 'app'],
        antonyms: ['hardware'],
        collocations: ['install software', 'software update', 'software developer'],
        frequency: 4,
        difficulty: 3,
        tags: ['technology', 'computers']
      },
      {
        word: 'download',
        pronunciation: '/ˈdaʊnloʊd/',
        phonetic: 'DOWN-lohd',
        partOfSpeech: 'verb',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'To copy data from the internet to your computer',
            example: 'I need to download this file from the website.',
            translation: 'Tải xuống'
          }
        ],
        synonyms: ['transfer', 'copy', 'save'],
        antonyms: ['upload'],
        collocations: ['download file', 'download speed', 'free download'],
        frequency: 4,
        difficulty: 2,
        tags: ['technology', 'internet']
      }
    ],

    'family-relationships': [
      {
        word: 'sibling',
        pronunciation: '/ˈsɪblɪŋ/',
        phonetic: 'SIB-ling',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'A brother or sister',
            example: 'I have two siblings - one brother and one sister.',
            translation: 'Anh chị em ruột'
          }
        ],
        synonyms: ['brother', 'sister'],
        antonyms: [],
        collocations: ['younger sibling', 'older sibling', 'sibling rivalry'],
        frequency: 3,
        difficulty: 3,
        tags: ['family', 'relationships']
      },
      {
        word: 'relative',
        pronunciation: '/ˈrelətɪv/',
        phonetic: 'REL-uh-tiv',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'A person who is part of your family',
            example: 'All my relatives are coming to the wedding.',
            translation: 'Họ hàng'
          }
        ],
        synonyms: ['family member', 'relation', 'kin'],
        antonyms: ['stranger'],
        collocations: ['close relative', 'distant relative', 'family relatives'],
        frequency: 3,
        difficulty: 2,
        tags: ['family', 'relationships']
      }
    ],

    'weather-climate': [
      {
        word: 'temperature',
        pronunciation: '/ˈtemprətʃər/',
        phonetic: 'TEM-pruh-chur',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'How hot or cold something is',
            example: 'The temperature today is 25 degrees Celsius.',
            translation: 'Nhiệt độ'
          }
        ],
        synonyms: ['heat level', 'thermal reading'],
        antonyms: [],
        collocations: ['high temperature', 'low temperature', 'room temperature'],
        frequency: 4,
        difficulty: 2,
        tags: ['weather', 'measurement']
      },
      {
        word: 'forecast',
        pronunciation: '/ˈfɔːrkæst/',
        phonetic: 'FOR-kast',
        partOfSpeech: 'noun',
        level: 'intermediate',
        definitions: [
          {
            meaning: 'A prediction of future weather conditions',
            example: 'The weather forecast says it will rain tomorrow.',
            translation: 'Dự báo thời tiết'
          }
        ],
        synonyms: ['prediction', 'outlook', 'projection'],
        antonyms: [],
        collocations: ['weather forecast', 'forecast accuracy', 'long-term forecast'],
        frequency: 3,
        difficulty: 3,
        tags: ['weather', 'prediction']
      }
    ],

    'shopping-money': [
      {
        word: 'receipt',
        pronunciation: '/rɪˈsiːt/',
        phonetic: 'ri-SEET',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'A piece of paper that shows you paid for something',
            example: 'Keep your receipt in case you need to return the item.',
            translation: 'Hóa đơn'
          }
        ],
        synonyms: ['bill', 'invoice', 'proof of purchase'],
        antonyms: [],
        collocations: ['sales receipt', 'keep receipt', 'receipt number'],
        frequency: 4,
        difficulty: 2,
        tags: ['shopping', 'money']
      },
      {
        word: 'discount',
        pronunciation: '/ˈdɪskaʊnt/',
        phonetic: 'DIS-kownt',
        partOfSpeech: 'noun',
        level: 'beginner',
        definitions: [
          {
            meaning: 'A reduction in the usual price of something',
            example: 'There\'s a 20% discount on all shoes this week.',
            translation: 'Giảm giá'
          }
        ],
        synonyms: ['reduction', 'sale price', 'markdown'],
        antonyms: ['markup', 'surcharge'],
        collocations: ['student discount', 'bulk discount', 'discount code'],
        frequency: 4,
        difficulty: 2,
        tags: ['shopping', 'money']
      }
    ]
  }
};

async function seedVocabulary() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data
    await VocabularyTopic.deleteMany({});
    await VocabularyWord.deleteMany({});
    console.log('Cleared existing vocabulary data');

    // Insert topics
    const createdTopics = await VocabularyTopic.insertMany(vocabularyData.topics);
    console.log(`Created ${createdTopics.length} vocabulary topics`);

    // Insert words for each topic
    let totalWords = 0;
    for (const topic of createdTopics) {
      const wordsForTopic = vocabularyData.words[topic.slug] || [];
      
      if (wordsForTopic.length > 0) {
        const wordsWithTopicId = wordsForTopic.map(word => ({
          ...word,
          topicId: topic._id,
          language: 'en'
        }));

        await VocabularyWord.insertMany(wordsWithTopicId);
        
        // Update topic word count
        await VocabularyTopic.findByIdAndUpdate(
          topic._id,
          { wordCount: wordsForTopic.length }
        );
        
        totalWords += wordsForTopic.length;
        console.log(`Added ${wordsForTopic.length} words to topic: ${topic.name}`);
      }
    }

    console.log(`\n✅ Vocabulary seeding completed!`);
    console.log(`📚 Total topics created: ${createdTopics.length}`);
    console.log(`📝 Total words created: ${totalWords}`);

  } catch (error) {
    console.error('Error seeding vocabulary:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the seeding function
seedVocabulary();

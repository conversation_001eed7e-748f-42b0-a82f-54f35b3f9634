'use client';

import { useState } from 'react';

export default function SpacingDebug() {
  const [showDebug, setShowDebug] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Debug Toggle Button */}
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="fixed bottom-4 right-4 z-[9999] bg-red-500 text-white px-3 py-2 rounded-lg text-xs font-mono shadow-lg hover:bg-red-600 transition-colors"
        style={{ fontSize: '10px' }}
      >
        {showDebug ? 'Hide' : 'Show'} Spacing
      </button>

      {/* Debug Overlay */}
      {showDebug && (
        <div className="fixed inset-0 pointer-events-none z-[9998]">
          {/* Header Area */}
          <div className="absolute top-0 left-0 right-0 h-16 bg-red-500/20 border-b-2 border-red-500">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-red-500 text-white px-2 py-1 rounded text-xs font-mono">
              Header (64px)
            </div>
          </div>
          
          {/* Content Start Area */}
          <div className="absolute top-16 left-0 right-0 h-8 bg-green-500/20 border-b-2 border-green-500">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-500 text-white px-2 py-1 rounded text-xs font-mono">
              Gap (32px)
            </div>
          </div>
          
          {/* Expected Content Start */}
          <div className="absolute top-24 left-0 right-0 h-1 bg-blue-500">
            <div className="absolute left-4 -top-6 bg-blue-500 text-white px-2 py-1 rounded text-xs font-mono">
              Content should start here (pt-24 = 96px)
            </div>
          </div>
        </div>
      )}
    </>
  );
}

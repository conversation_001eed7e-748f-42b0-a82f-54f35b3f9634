'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, Level } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import AdSense from '@/components/AdSense';

interface Props {
  level: Level;
}

export default function VocabularyLevelClient({ level }: Props) {
  const router = useRouter();
  const { t, language } = useLanguage();
  const [topics, setTopics] = useState<VocabularyTopic[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);

  useEffect(() => {
    fetchTopics();
  }, [language, level]);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        language,
        level,
        limit: '50',
      });

      const response = await fetch(`/api/vocabulary/topics?${params}`);
      const data = await response.json();

      if (data.success) {
        setTopics(data.data.topics);
      } else {
        console.error('Failed to fetch topics:', data.error);
      }
    } catch (error) {
      console.error('Error fetching topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTopicClick = (slug: string) => {
    router.push(`/vocabulary/${slug}`);
  };

  const getLevelText = () => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getPageTitle = () => {
    const levelText = getLevelText();
    switch (language) {
      case 'vi':
        return `Từ Vựng ${levelText}`;
      case 'zh':
        return `${levelText}词汇`;
      default:
        return `${levelText} Vocabulary`;
    }
  };

  const getPageDescription = () => {
    const levelText = getLevelText().toLowerCase();
    switch (language) {
      case 'vi':
        return `Học từ vựng tiếng Anh cấp độ ${levelText} với các chủ đề phong phú và bài tập thực hành.`;
      case 'zh':
        return `学习${levelText}英语词汇，包含丰富的主题和练习。`;
      default:
        return `Learn ${levelText} English vocabulary with diverse topics and practice exercises.`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header 
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => router.push('/vocabulary')}
      />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <div className="w-16 h-16 rounded-xl bg-blue-100 flex items-center justify-center text-3xl mr-4">
                {level === 'beginner' ? '🌱' : level === 'intermediate' ? '🌿' : '🌳'}
              </div>
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                  {getPageTitle()}
                </h1>
                <span className="text-lg text-gray-600 bg-white px-4 py-2 rounded-full shadow-sm">
                  {getLevelText()}
                </span>
              </div>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
            <div className="mt-4 text-gray-500">
              {topics.length} {language === 'vi' ? 'chủ đề' : language === 'zh' ? '个主题' : 'topics'}
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense />
          </div>

          {/* Topics Grid */}
          {topics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {topics.map((topic) => (
                <div
                  key={topic._id}
                  onClick={() => handleTopicClick(topic.slug)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                      style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                    >
                      {topic.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900 mb-1">
                        {topic.name}
                      </h3>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {topic.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {topic.wordCount} {language === 'vi' ? 'từ' : language === 'zh' ? '个词' : 'words'}
                    </span>
                    <div className="text-blue-500 text-sm font-medium">
                      {language === 'vi' ? 'Học ngay →' : language === 'zh' ? '立即学习 →' : 'Learn now →'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {language === 'vi' ? 'Chưa có chủ đề nào' : language === 'zh' ? '暂无主题' : 'No topics available'}
              </h3>
              <p className="text-gray-600">
                {language === 'vi' 
                  ? `Các chủ đề từ vựng cấp độ ${getLevelText().toLowerCase()} sẽ được cập nhật sớm.` 
                  : language === 'zh' 
                  ? `${getLevelText()}词汇主题即将更新。` 
                  : `${getLevelText()} vocabulary topics will be available soon.`
                }
              </p>
            </div>
          )}

          {/* Level Navigation */}
          <div className="mt-12 flex justify-center">
            <div className="bg-white rounded-xl p-4 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                {language === 'vi' ? 'Các cấp độ khác' : language === 'zh' ? '其他级别' : 'Other Levels'}
              </h3>
              <div className="flex space-x-4">
                {(['beginner', 'intermediate', 'advanced'] as Level[]).map((lvl) => (
                  <button
                    key={lvl}
                    onClick={() => router.push(`/vocabulary/${lvl}`)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      level === lvl
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {lvl === 'beginner' 
                      ? (language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner')
                      : lvl === 'intermediate'
                      ? (language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate')
                      : (language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced')
                    }
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      
      <Footer />
    </div>
  );
}

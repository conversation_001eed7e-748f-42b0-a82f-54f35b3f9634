import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import VocabularyWord from '@/models/VocabularyWord';
import VocabularyTopic from '@/models/VocabularyTopic';
import { Language, Level } from '@/types';

// GET - Get vocabulary words
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const topicSlug = searchParams.get('topic');
    const language = searchParams.get('language') as Language || 'en';
    const level = searchParams.get('level') as Level;
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    // Build query
    const query: any = {
      isActive: true,
      language,
    };

    // Filter by topic if provided
    if (topicSlug) {
      const topic = await VocabularyTopic.findOne({ slug: topicSlug, isActive: true });
      if (!topic) {
        return NextResponse.json(
          { success: false, error: 'Topic not found' },
          { status: 404 }
        );
      }
      query.topicId = topic._id;
    }

    // Filter by level if provided
    if (level) {
      query.level = level;
    }

    // Search functionality
    if (search) {
      query.$or = [
        { word: { $regex: search, $options: 'i' } },
        { 'definitions.meaning': { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Get words with pagination
    const skip = (page - 1) * limit;
    const words = await VocabularyWord.find(query)
      .populate('topicId', 'name slug icon color')
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await VocabularyWord.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: {
        words,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching vocabulary words:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch vocabulary words' },
      { status: 500 }
    );
  }
}

// POST - Create new vocabulary word
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      topicId,
      word,
      pronunciation,
      phonetic,
      partOfSpeech,
      level,
      language,
      definitions,
      synonyms,
      antonyms,
      collocations,
      frequency,
      difficulty,
      audioUrl,
      imageUrl,
      tags,
      order,
    } = body;

    // Validation
    if (!topicId || !word || !pronunciation || !phonetic || !partOfSpeech || !level || !definitions || definitions.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if topic exists
    const topic = await VocabularyTopic.findById(topicId);
    if (!topic) {
      return NextResponse.json(
        { success: false, error: 'Topic not found' },
        { status: 404 }
      );
    }

    // Create new word
    const vocabularyWord = new VocabularyWord({
      topicId,
      word: word.toLowerCase(),
      pronunciation,
      phonetic,
      partOfSpeech,
      level,
      language: language || 'en',
      definitions,
      synonyms: synonyms || [],
      antonyms: antonyms || [],
      collocations: collocations || [],
      frequency: frequency || 3,
      difficulty: difficulty || 3,
      audioUrl,
      imageUrl,
      tags: tags || [],
      order: order || 0,
    });

    await vocabularyWord.save();

    // Update topic word count
    await VocabularyTopic.findByIdAndUpdate(
      topicId,
      { $inc: { wordCount: 1 } }
    );

    return NextResponse.json({
      success: true,
      data: vocabularyWord,
    });
  } catch (error) {
    console.error('Error creating vocabulary word:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create vocabulary word' },
      { status: 500 }
    );
  }
}

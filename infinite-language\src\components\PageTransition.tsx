'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';

interface PageTransitionProps {
  children: React.ReactNode;
}

export default function PageTransition({ children }: PageTransitionProps) {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [targetUrl, setTargetUrl] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useLanguage();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousPathnameRef = useRef(pathname);

  // Reset transition state when pathname changes
  useEffect(() => {
    if (previousPathnameRef.current !== pathname) {
      setIsTransitioning(false);
      setTargetUrl(null);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      previousPathnameRef.current = pathname;
    }
  }, [pathname]);

  useEffect(() => {
    const handleRouteChange = () => {
      setIsTransitioning(false);
      setTargetUrl(null);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Override link clicks to add transition
  useEffect(() => {
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;

      if (link && link.href && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {
        const url = new URL(link.href);
        const currentUrl = new URL(window.location.href);

        // Only intercept internal links and avoid same-page navigation
        if (url.origin === currentUrl.origin && !link.target &&
            (url.pathname + url.search) !== (currentUrl.pathname + currentUrl.search)) {
          e.preventDefault();
          setTargetUrl(url.pathname + url.search);
          setIsTransitioning(true);

          // Clear any existing timeout
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
          }

          // Navigate after a short delay to show transition
          timeoutRef.current = setTimeout(() => {
            router.push(url.pathname + url.search);

            // Fallback: reset transition state after 5 seconds if navigation doesn't complete
            timeoutRef.current = setTimeout(() => {
              setIsTransitioning(false);
              setTargetUrl(null);
            }, 5000);
          }, 300);
        }
      }
    };

    document.addEventListener('click', handleLinkClick);

    return () => {
      document.removeEventListener('click', handleLinkClick);
    };
  }, [router]);

  if (isTransitioning) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="text-center relative z-10">
          {/* Modern loading animation */}
          <div className="relative mb-8">
            {/* Outer ring */}
            <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 mx-auto"></div>
            {/* Inner ring */}
            <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-500 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            {/* Center icon */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center animate-pulse">
                <span className="text-white text-lg">🚀</span>
              </div>
            </div>
          </div>

          {/* Loading text */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl px-8 py-4 shadow-xl border border-white/20">
            <p className="text-gray-800 text-xl font-semibold mb-2">
              {t('navigating') || 'Navigating...'}
            </p>
            <p className="text-gray-600 text-sm">
              {targetUrl && `Going to ${targetUrl}`}
            </p>
          </div>

          {/* Loading dots animation */}
          <div className="flex justify-center space-x-2 mt-6">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce delay-100"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-200"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="transition-opacity duration-300 ease-in-out">
      {children}
    </div>
  );
}

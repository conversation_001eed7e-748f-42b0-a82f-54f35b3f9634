<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0e7ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main circle background -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#1e40af" stroke-width="2"/>
  
  <!-- Book icon -->
  <rect x="18" y="20" width="28" height="20" rx="2" fill="url(#textGradient)" stroke="#1e40af" stroke-width="1"/>
  <line x1="32" y1="20" x2="32" y2="40" stroke="#3b82f6" stroke-width="1"/>
  
  <!-- Text lines representing content -->
  <line x1="21" y1="26" x2="29" y2="26" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  <line x1="21" y1="30" x2="28" y2="30" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  <line x1="21" y1="34" x2="29" y2="34" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  
  <line x1="35" y1="26" x2="43" y2="26" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  <line x1="35" y1="30" x2="42" y2="30" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  <line x1="35" y1="34" x2="43" y2="34" stroke="#3b82f6" stroke-width="1.5" stroke-linecap="round"/>
  
  <!-- AI/Infinity symbol -->
  <path d="M20 45 Q26 42 32 45 Q38 48 44 45" stroke="#ffffff" stroke-width="2" fill="none" stroke-linecap="round"/>
  <circle cx="22" cy="45" r="1.5" fill="#ffffff"/>
  <circle cx="42" cy="45" r="1.5" fill="#ffffff"/>
  
  <!-- Language symbols -->
  <text x="14" y="54" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#ffffff">EN</text>
  <text x="28" y="54" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#ffffff">VI</text>
  <text x="42" y="54" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#ffffff">中</text>
</svg>

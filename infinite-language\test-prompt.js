// Test script to verify the improved SHORT prompt system
// Run with: node test-prompt.js

const { generateQuestionWithGemini } = require('./src/lib/gemini.ts');

async function testPromptSystem() {
  console.log('🧪 Testing SHORT TOEIC/IELTS style prompt system...\n');

  const levels = ['beginner', 'intermediate', 'advanced'];
  const languages = ['en', 'vi'];

  for (const level of levels) {
    console.log(`\n📚 Testing ${level.toUpperCase()} level:`);
    console.log('='.repeat(60));

    for (const language of languages) {
      try {
        console.log(`\n🌐 Language: ${language}`);
        const question = await generateQuestionWithGemini(level, language);

        console.log(`📝 Question: "${question.question}"`);
        console.log(`📋 Options:`);
        question.options.forEach((option, index) => {
          const marker = index === question.correctAnswer ? '✅' : '  ';
          console.log(`   ${String.fromCharCode(65 + index)}. ${option} ${marker}`);
        });
        console.log(`💡 Explanation: ${question.explanation}`);
        console.log(`🎯 Level: ${question.level}`);
        console.log(`📏 Question Length: ${question.question.length} characters`);

        // Check if question is appropriately short
        if (question.question.length > 100) {
          console.log(`⚠️  WARNING: Question might be too long (${question.question.length} chars)`);
        } else {
          console.log(`✅ Good length: ${question.question.length} characters`);
        }

        console.log('-'.repeat(50));

        // Wait a bit to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Error testing ${level}-${language}:`, error.message);
      }
    }
  }

  console.log('\n✅ Test completed!');
}

// Test cache system
async function testCacheSystem() {
  console.log('\n🔄 Testing cache system (generating same level/language multiple times)...\n');
  
  const level = 'intermediate';
  const language = 'en';
  const questions = [];
  
  for (let i = 0; i < 5; i++) {
    try {
      console.log(`Generating question ${i + 1}/5...`);
      const question = await generateQuestionWithGemini(level, language);
      questions.push(question.question);
      console.log(`✅ Generated: ${question.question.substring(0, 50)}...`);
      
      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`❌ Error generating question ${i + 1}:`, error.message);
    }
  }
  
  // Check for duplicates
  const uniqueQuestions = new Set(questions);
  console.log(`\n📊 Results:`);
  console.log(`Total questions generated: ${questions.length}`);
  console.log(`Unique questions: ${uniqueQuestions.size}`);
  console.log(`Duplicates: ${questions.length - uniqueQuestions.size}`);
  
  if (uniqueQuestions.size === questions.length) {
    console.log('✅ Cache system working - no duplicates found!');
  } else {
    console.log('⚠️ Some duplicates found - cache system may need adjustment');
  }
}

// Run tests
async function runAllTests() {
  try {
    await testPromptSystem();
    await testCacheSystem();
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { testPromptSystem, testCacheSystem };

'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDropdown } from '@/contexts/DropdownContext';
import Link from 'next/link';

interface UserMenuProps {
  onHistoryClick: () => void;
}

export default function UserMenu({ onHistoryClick }: UserMenuProps) {
  const { user, logout } = useAuth();
  const { t } = useLanguage();
  const { openDropdown, setOpenDropdown } = useDropdown();

  const isOpen = openDropdown === 'user';

  if (!user) return null;

  const handleLogout = async () => {
    await logout();
    setOpenDropdown(null);
  };

  const toggleDropdown = () => {
    setOpenDropdown(isOpen ? null : 'user');
  };

  const accuracy = user.stats.totalQuestions > 0 
    ? Math.round((user.stats.correctAnswers / user.stats.totalQuestions) * 100)
    : 0;

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className="group flex items-center space-x-3 bg-white/80 hover:bg-white/90 rounded-xl px-4 py-2.5 shadow-sm hover:shadow-md transition-all duration-500 backdrop-blur-sm"
      >
        <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-sm group-hover:scale-105 transition-transform duration-200">
          {user.username.charAt(0).toUpperCase()}
        </div>
        <div className="hidden sm:block text-left">
          <div className="text-sm font-semibold text-gray-800">{user.username}</div>
          <div className="text-xs text-gray-500">{accuracy}% {t('accuracyText')}</div>
        </div>
        <svg
          className={`w-4 h-4 text-gray-500 transition-all duration-300 ${
            isOpen ? 'rotate-180 text-gray-700' : 'group-hover:text-gray-700'
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-72 bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl z-50">
          {/* User Info */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                {user.username.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1">
                <div className="font-bold text-gray-900 text-lg">{user.username}</div>
                <div className="text-sm text-gray-500">{user.email}</div>
                <div className="flex items-center space-x-1 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">Active learner</span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="p-6 border-b border-gray-100">
            <div className="text-sm font-bold text-gray-800 mb-4 flex items-center">
              <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              {t('stats') || 'Learning Statistics'}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-xl p-3 text-center">
                <div className="text-2xl font-bold text-blue-600">{user.stats.totalQuestions}</div>
                <div className="text-xs text-blue-500 font-medium">{t('totalQuestions') || 'Total Questions'}</div>
              </div>
              <div className="bg-emerald-50 rounded-xl p-3 text-center">
                <div className="text-2xl font-bold text-emerald-600">{accuracy}%</div>
                <div className="text-xs text-emerald-500 font-medium">{t('accuracy') || 'Accuracy'}</div>
              </div>
              <div className="bg-orange-50 rounded-xl p-3 text-center">
                <div className="text-2xl font-bold text-orange-600">{user.stats.streakCount}</div>
                <div className="text-xs text-orange-500 font-medium">{t('streak') || 'Day Streak'}</div>
              </div>
              <div className="bg-purple-50 rounded-xl p-3 text-center">
                <div className="text-2xl font-bold text-purple-600">{user.stats.correctAnswers}</div>
                <div className="text-xs text-purple-500 font-medium">{t('correct') || 'Correct'}</div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="p-3">
            <Link
              href="/dashboard"
              onClick={() => setOpenDropdown(null)}
              className="w-full flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-green-50 hover:text-green-700 rounded-xl transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-green-100 group-hover:bg-green-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <span className="font-medium">{t('learningDashboard')}</span>
            </Link>
            <button
              onClick={() => {
                onHistoryClick();
                setOpenDropdown(null);
              }}
              className="w-full flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-xl transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-blue-100 group-hover:bg-blue-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="font-medium">{t('viewHistory') || 'View Learning History'}</span>
            </button>
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200 group"
            >
              <div className="w-8 h-8 bg-red-100 group-hover:bg-red-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </div>
              <span className="font-medium">{t('logout') || 'Sign Out'}</span>
            </button>
          </div>
        </div>
      )}

      {/* Overlay to close dropdown when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setOpenDropdown(null)}
        />
      )}
    </div>
  );
}

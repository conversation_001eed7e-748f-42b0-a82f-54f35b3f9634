'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, VocabularyWord } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import VocabularyWordCard from '@/components/VocabularyWordCard';
import AdSense from '@/components/AdSense';

interface Props {
  slug: string;
}

export default function VocabularyTopicClient({ slug }: Props) {
  const router = useRouter();
  const { t, language } = useLanguage();
  const [topic, setTopic] = useState<VocabularyTopic | null>(null);
  const [words, setWords] = useState<VocabularyWord[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchTopicData();
  }, [slug, language, currentPage, searchTerm]);

  const fetchTopicData = async () => {
    try {
      setLoading(true);
      
      // Fetch topic info
      const topicResponse = await fetch(`/api/vocabulary/topics/${slug}`);
      const topicData = await topicResponse.json();

      if (!topicData.success) {
        router.push('/vocabulary');
        return;
      }

      setTopic(topicData.data.topic);

      // Fetch words for this topic
      const params = new URLSearchParams({
        topic: slug,
        language,
        page: currentPage.toString(),
        limit: '20',
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const wordsResponse = await fetch(`/api/vocabulary/words?${params}`);
      const wordsData = await wordsResponse.json();

      if (wordsData.success) {
        setWords(wordsData.data.words);
        setTotalPages(wordsData.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching topic data:', error);
      router.push('/vocabulary');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchTopicData();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getLevelText = (level: string) => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  if (!topic) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24 text-center">
          <h1 className="text-2xl font-bold text-gray-900">Topic not found</h1>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header 
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => router.push('/vocabulary')}
      />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Topic Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div 
                className="w-16 h-16 rounded-xl flex items-center justify-center text-3xl mr-4"
                style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
              >
                {topic.icon}
              </div>
              <div>
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                  {topic.name}
                </h1>
                <span className="text-lg text-gray-600 bg-white px-4 py-2 rounded-full shadow-sm">
                  {getLevelText(topic.level)}
                </span>
              </div>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {topic.description}
            </p>
            <div className="mt-4 text-gray-500">
              {topic.wordCount} {language === 'vi' ? 'từ vựng' : language === 'zh' ? '个词汇' : 'vocabulary words'}
            </div>
          </div>

          {/* Search Bar */}
          <div className="max-w-md mx-auto mb-8">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={language === 'vi' ? 'Tìm kiếm từ vựng...' : language === 'zh' ? '搜索词汇...' : 'Search vocabulary...'}
                className="w-full px-4 py-3 pl-12 pr-4 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                🔍
              </div>
            </form>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense />
          </div>

          {/* Words Grid */}
          {words.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {words.map((word) => (
                  <VocabularyWordCard key={word._id} word={word} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center">
                  <div className="flex space-x-2">
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                          currentPage === page
                            ? 'bg-blue-500 text-white shadow-md'
                            : 'bg-white text-gray-600 hover:bg-gray-100 shadow-sm'
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchTerm 
                  ? (language === 'vi' ? 'Không tìm thấy từ nào' : language === 'zh' ? '未找到词汇' : 'No words found')
                  : (language === 'vi' ? 'Chưa có từ vựng' : language === 'zh' ? '暂无词汇' : 'No vocabulary available')
                }
              </h3>
              <p className="text-gray-600">
                {searchTerm 
                  ? (language === 'vi' ? 'Thử tìm kiếm với từ khóa khác.' : language === 'zh' ? '尝试使用其他关键词搜索。' : 'Try searching with different keywords.')
                  : (language === 'vi' ? 'Từ vựng sẽ được cập nhật sớm.' : language === 'zh' ? '词汇即将更新。' : 'Vocabulary will be available soon.')
                }
              </p>
            </div>
          )}
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      
      <Footer />
    </div>
  );
}

import { Metadata } from 'next';
import { headers } from 'next/headers';
import VocabularyLevelServerPage from '@/components/VocabularyLevelServerPage';
import { VocabularyTopic } from '@/types';
import connectDB from '@/lib/mongodb';
import VocabularyTopicModel from '@/models/VocabularyTopic';

export const metadata: Metadata = {
  title: 'Beginner Vocabulary - Infinite English',
  description: 'Learn beginner-level English vocabulary with easy words, definitions, and examples. Perfect for English language learners starting their journey.',
  keywords: 'beginner vocabulary, basic English words, elementary vocabulary, English for beginners, simple English words',
  openGraph: {
    title: 'Beginner Vocabulary - Infinite English',
    description: 'Start your English learning journey with beginner vocabulary.',
    type: 'website',
  },
};

async function getBeginnerTopics(language: string = 'vi'): Promise<VocabularyTopic[]> {
  try {
    await connectDB();

    const topics = await VocabularyTopicModel
      .find({
        language,
        level: 'beginner',
        isActive: true
      })
      .sort({ order: 1, createdAt: -1 })
      .limit(50)
      .lean();

    return topics.map(topic => ({
      ...topic,
      _id: topic._id.toString(),
      createdAt: topic.createdAt.toISOString(),
      updatedAt: topic.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching beginner vocabulary topics:', error);
    return [];
  }
}

export default async function BeginnerVocabularyPage() {
  // Get language from headers or default to Vietnamese
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const language = acceptLanguage.includes('zh') ? 'zh' :
                  acceptLanguage.includes('en') ? 'en' : 'vi';

  const topics = await getBeginnerTopics(language);

  return <VocabularyLevelServerPage level="beginner" initialTopics={topics} />;
}

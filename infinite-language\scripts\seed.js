const { exec } = require('child_process');

console.log('🌱 Starting database seeding...');

// Function to make HTTP request to seed endpoint
async function seedDatabase() {
  try {
    const response = await fetch('http://localhost:3000/api/seed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Database seeded successfully!');
      console.log('📊 Results:');
      console.log(`   - Lessons: ${data.results.lessons.created} created, ${data.results.lessons.skipped} skipped`);
      console.log(`   - Vocabulary: ${data.results.vocabulary.created} created, ${data.results.vocabulary.skipped} skipped`);
    } else {
      console.error('❌ Failed to seed database:', data.error);
    }
  } catch (error) {
    console.error('❌ Error seeding database:', error.message);
    console.log('💡 Make sure your development server is running on http://localhost:3000');
  }
}

// Check if server is running and seed
seedDatabase();

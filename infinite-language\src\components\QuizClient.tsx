'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Question, Level, Language, QuestionResult } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { useQuizScore } from '@/hooks/useQuizScore';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import QuestionCard from '@/components/QuestionCard';
import ResultCard from '@/components/ResultCard';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import SkeletonLoader from '@/components/SkeletonLoader';
import AdSense from '@/components/AdSense';

interface QuizClientProps {
  initialQuestion: Question;
  level: Level;
  language: Language;
}

interface QuizState {
  currentQuestion: Question | null;
  selectedAnswer: number | null;
  showResult: boolean;
  isTransitioning: boolean;
  questionResult: QuestionResult | null;
}

export default function QuizClient({ initialQuestion, level, language }: QuizClientProps) {
  const router = useRouter();
  const { user } = useAuth();
  const { score, updateScore, resetScore } = useQuizScore(level);

  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestion: initialQuestion,
    selectedAnswer: null,
    showResult: false,
    isTransitioning: false,
    questionResult: null
  });

  const [showAuthModal, setShowAuthModal] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [isLoading, setIsLoading] = useState(false);

  const handleAnswerSelect = async (answerIndex: number) => {
    if (quizState.showResult || !quizState.currentQuestion) return;

    setQuizState(prev => ({
      ...prev,
      selectedAnswer: answerIndex
    }));

    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

    try {
      // Submit answer to server for validation
      const response = await fetch('/api/check-answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId: quizState.currentQuestion.id,
          userAnswer: answerIndex
        }),
      });

      const data = await response.json();

      if (data.success && data.result) {
        const isCorrect = data.result.isCorrect;

        // Update quiz state with result
        setTimeout(() => {
          setQuizState(prev => ({
            ...prev,
            showResult: true,
            questionResult: data.result
          }));
          // Update score using the hook
          updateScore(isCorrect);
        }, 500);

        // Save to history if user is logged in
        if (user && quizState.currentQuestion) {
          try {
            await fetch('/api/quiz/history', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                questionId: quizState.currentQuestion.id,
                question: quizState.currentQuestion.question,
                options: quizState.currentQuestion.options,
                correctAnswer: data.result.correctAnswer,
                userAnswer: answerIndex,
                explanation: data.result.explanation,
                level: quizState.currentQuestion.level,
                language: language,
                timeSpent: timeSpent,
              }),
            });
          } catch (error) {
            console.error('Failed to save quiz history:', error);
          }
        }
      } else {
        console.error('Failed to submit answer:', data.error);
        return;
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      return;
    }
  };

  const handleNextQuestion = async () => {
    // Start transition immediately
    setQuizState(prev => ({
      ...prev,
      isTransitioning: true
    }));

    // Small delay to allow transition to start, then clear content
    setTimeout(() => {
      setQuizState(prev => ({
        ...prev,
        currentQuestion: null,
        selectedAnswer: null,
        showResult: false,
        questionResult: null
      }));
      setIsLoading(true);
    }, 150);

    try {
      const response = await fetch('/api/quiz/next-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ level, language }),
      });

      const data = await response.json();

      if (data.success && data.question) {
        setQuizState({
          currentQuestion: data.question,
          selectedAnswer: null,
          showResult: false,
          isTransitioning: false,
          questionResult: null
        });
        // Reset question start time for new question
        setQuestionStartTime(Date.now());
      } else {
        console.error('Failed to generate next question:', data.error);
        // Fallback to page refresh if API fails
        router.refresh();
      }
    } catch (error) {
      console.error('Error fetching next question:', error);
      // Fallback to page refresh if API fails
      router.refresh();
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLevels = () => {
    router.push('/');
  };

  const handleResetScore = () => {
    resetScore();
  };

  // Show loading when generating next question or when currentQuestion is null
  if (isLoading || !quizState.currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className="pt-24">
          <SkeletonLoader variant="question" />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion && quizState.showResult && quizState.questionResult) {
    const isCorrect = quizState.questionResult.isCorrect;
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className={`pt-24 transition-opacity duration-300 ${quizState.isTransitioning ? 'opacity-0' : 'opacity-100'}`}>
          <ResultCard
            question={{
              ...quizState.currentQuestion,
              correctAnswer: quizState.questionResult.correctAnswer,
              explanation: quizState.questionResult.explanation
            }}
            selectedAnswer={quizState.selectedAnswer}
            isCorrect={isCorrect}
            onNextQuestion={handleNextQuestion}
            score={score}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={score}
          onResetScore={handleResetScore}
        />
        <div className={`pt-24 transition-opacity duration-300 ${quizState.isTransitioning ? 'opacity-0' : 'opacity-100'}`}>
          <QuestionCard
            question={quizState.currentQuestion}
            selectedAnswer={quizState.selectedAnswer}
            onAnswerSelect={handleAnswerSelect}
            showResult={quizState.showResult}
          />

          {/* AdSense Banner */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <AdSense
              adSlot="1234567890"
              adFormat="auto"
              className="rounded-lg"
              style={{ display: 'block', minHeight: '100px' }}
              lazy={true}
            />
          </div>

          {/* Educational Content */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                {language === 'vi' ? 'Các Chủ Đề Luyện Tập Phổ Biến' : 'Popular Practice Topics'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">
                    {language === 'vi' ? 'Ngữ Pháp Cơ Bản' : 'Basic Grammar'}
                  </h4>
                  <ul className="text-blue-700 text-sm space-y-1">
                    <li>• {language === 'vi' ? 'Thì hiện tại đơn' : 'Present Simple'}</li>
                    <li>• {language === 'vi' ? 'Thì quá khứ đơn' : 'Past Simple'}</li>
                    <li>• {language === 'vi' ? 'Động từ khuyết thiếu' : 'Modal Verbs'}</li>
                  </ul>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">
                    {language === 'vi' ? 'Từ Vựng Thông Dụng' : 'Common Vocabulary'}
                  </h4>
                  <ul className="text-green-700 text-sm space-y-1">
                    <li>• {language === 'vi' ? 'Từ vựng hàng ngày' : 'Daily vocabulary'}</li>
                    <li>• {language === 'vi' ? 'Từ vựng công việc' : 'Work vocabulary'}</li>
                    <li>• {language === 'vi' ? 'Từ vựng du lịch' : 'Travel vocabulary'}</li>
                  </ul>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-2">
                    {language === 'vi' ? 'Kỹ Năng Giao Tiếp' : 'Communication Skills'}
                  </h4>
                  <ul className="text-purple-700 text-sm space-y-1">
                    <li>• {language === 'vi' ? 'Hội thoại cơ bản' : 'Basic conversations'}</li>
                    <li>• {language === 'vi' ? 'Thành ngữ phổ biến' : 'Common idioms'}</li>
                    <li>• {language === 'vi' ? 'Cách diễn đạt lịch sự' : 'Polite expressions'}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  // This should never be reached due to the loading check above
  return <LoadingSpinner />;
}

const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// Define schemas
const VocabularyTopicSchema = new mongoose.Schema({
  name: String,
  slug: String,
  description: String,
  icon: String,
  color: String,
  level: String,
  language: String,
  wordCount: Number,
  isActive: Boolean,
  order: Number,
  seoTitle: String,
  seoDescription: String,
  seoKeywords: [String],
}, { timestamps: true });

const VocabularyWordSchema = new mongoose.Schema({
  topicId: mongoose.Schema.Types.ObjectId,
  word: String,
  pronunciation: String,
  phonetic: String,
  partOfSpeech: String,
  level: String,
  language: String,
  definitions: [{
    meaning: String,
    example: String,
    translation: String,
  }],
  synonyms: [String],
  antonyms: [String],
  collocations: [String],
  frequency: Number,
  difficulty: Number,
  tags: [String],
  isActive: Boolean,
  order: Number,
}, { timestamps: true });

const VocabularyTopic = mongoose.model('VocabularyTopic', VocabularyTopicSchema);
const VocabularyWord = mongoose.model('VocabularyWord', VocabularyWordSchema);

// Additional vocabulary data
const additionalWords = {
  'business-work': [
    {
      word: 'colleague',
      pronunciation: '/ˈkɒliːɡ/',
      phonetic: 'KOL-eeg',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'A person you work with, especially in a profession or business',
          example: 'I discussed the project with my colleagues.',
          translation: 'Đồng nghiệp'
        }
      ],
      synonyms: ['coworker', 'associate', 'teammate'],
      antonyms: ['competitor', 'rival'],
      collocations: ['work colleague', 'close colleague', 'former colleague'],
      frequency: 4,
      difficulty: 3,
      tags: ['business', 'workplace']
    },
    {
      word: 'schedule',
      pronunciation: '/ˈʃedjuːl/',
      phonetic: 'SHED-yool',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'A plan that lists activities or events and when they will happen',
          example: 'My schedule is very busy this week.',
          translation: 'Lịch trình'
        }
      ],
      synonyms: ['timetable', 'agenda', 'calendar'],
      antonyms: [],
      collocations: ['busy schedule', 'work schedule', 'schedule meeting'],
      frequency: 4,
      difficulty: 2,
      tags: ['business', 'time management']
    },
    {
      word: 'salary',
      pronunciation: '/ˈsæləri/',
      phonetic: 'SAL-uh-ree',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'Money that you receive regularly for doing a job',
          example: 'She negotiated a higher salary for her new position.',
          translation: 'Lương'
        }
      ],
      synonyms: ['wage', 'pay', 'income'],
      antonyms: [],
      collocations: ['annual salary', 'monthly salary', 'salary increase'],
      frequency: 4,
      difficulty: 2,
      tags: ['business', 'money']
    },
    {
      word: 'interview',
      pronunciation: '/ˈɪntəvjuː/',
      phonetic: 'IN-ter-vyoo',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'A formal meeting where someone asks you questions to see if you are suitable for a job',
          example: 'I have a job interview tomorrow morning.',
          translation: 'Phỏng vấn'
        }
      ],
      synonyms: ['meeting', 'assessment', 'evaluation'],
      antonyms: [],
      collocations: ['job interview', 'phone interview', 'interview questions'],
      frequency: 4,
      difficulty: 3,
      tags: ['business', 'employment']
    },
    {
      word: 'contract',
      pronunciation: '/ˈkɒntrækt/',
      phonetic: 'KON-trakt',
      partOfSpeech: 'noun',
      level: 'advanced',
      definitions: [
        {
          meaning: 'A legal agreement between two or more parties',
          example: 'Please read the contract carefully before signing.',
          translation: 'Hợp đồng'
        }
      ],
      synonyms: ['agreement', 'deal', 'pact'],
      antonyms: [],
      collocations: ['sign contract', 'employment contract', 'contract terms'],
      frequency: 4,
      difficulty: 4,
      tags: ['business', 'legal']
    }
  ],
  
  'travel-tourism': [
    {
      word: 'reservation',
      pronunciation: '/ˌrezəˈveɪʃən/',
      phonetic: 'rez-er-VAY-shuhn',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'An arrangement to have something kept for you',
          example: 'I made a reservation at the restaurant for 7 PM.',
          translation: 'Đặt chỗ'
        }
      ],
      synonyms: ['booking', 'appointment'],
      antonyms: ['cancellation'],
      collocations: ['hotel reservation', 'make reservation', 'cancel reservation'],
      frequency: 4,
      difficulty: 3,
      tags: ['travel', 'booking']
    },
    {
      word: 'destination',
      pronunciation: '/ˌdestɪˈneɪʃən/',
      phonetic: 'des-ti-NAY-shuhn',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'The place where someone or something is going',
          example: 'Paris is a popular tourist destination.',
          translation: 'Điểm đến'
        }
      ],
      synonyms: ['target', 'goal', 'endpoint'],
      antonyms: ['origin', 'starting point'],
      collocations: ['travel destination', 'final destination', 'popular destination'],
      frequency: 3,
      difficulty: 3,
      tags: ['travel', 'location']
    },
    {
      word: 'itinerary',
      pronunciation: '/aɪˈtɪnəreri/',
      phonetic: 'eye-TIN-uh-rer-ee',
      partOfSpeech: 'noun',
      level: 'advanced',
      definitions: [
        {
          meaning: 'A detailed plan of a journey or trip',
          example: 'Our travel itinerary includes visits to three cities.',
          translation: 'Lịch trình du lịch'
        }
      ],
      synonyms: ['schedule', 'plan', 'route'],
      antonyms: [],
      collocations: ['travel itinerary', 'detailed itinerary', 'flexible itinerary'],
      frequency: 2,
      difficulty: 4,
      tags: ['travel', 'planning']
    }
  ],
  
  'food-cooking': [
    {
      word: 'appetizer',
      pronunciation: '/ˈæpɪtaɪzər/',
      phonetic: 'AP-i-ty-zer',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'A small dish served before the main course',
          example: 'We ordered some appetizers to share.',
          translation: 'Món khai vị'
        }
      ],
      synonyms: ['starter', 'hors d\'oeuvre'],
      antonyms: ['main course', 'dessert'],
      collocations: ['cold appetizer', 'hot appetizer', 'appetizer menu'],
      frequency: 3,
      difficulty: 3,
      tags: ['food', 'restaurant']
    },
    {
      word: 'cuisine',
      pronunciation: '/kwɪˈziːn/',
      phonetic: 'kwi-ZEEN',
      partOfSpeech: 'noun',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'A style of cooking characteristic of a particular country or region',
          example: 'I love Italian cuisine, especially pasta dishes.',
          translation: 'Ẩm thực'
        }
      ],
      synonyms: ['cooking style', 'culinary tradition'],
      antonyms: [],
      collocations: ['local cuisine', 'international cuisine', 'authentic cuisine'],
      frequency: 3,
      difficulty: 3,
      tags: ['food', 'culture']
    },
    {
      word: 'vegetarian',
      pronunciation: '/ˌvedʒɪˈteəriən/',
      phonetic: 'vej-i-TAIR-ee-uhn',
      partOfSpeech: 'adjective',
      level: 'intermediate',
      definitions: [
        {
          meaning: 'Not containing meat or fish',
          example: 'Do you have any vegetarian options on the menu?',
          translation: 'Chay (không ăn thịt)'
        }
      ],
      synonyms: ['plant-based', 'meat-free'],
      antonyms: ['carnivorous', 'omnivorous'],
      collocations: ['vegetarian diet', 'vegetarian restaurant', 'vegetarian meal'],
      frequency: 3,
      difficulty: 3,
      tags: ['food', 'diet']
    }
  ]
};

async function addMoreVocabulary() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    let totalWordsAdded = 0;

    for (const [topicSlug, words] of Object.entries(additionalWords)) {
      // Find the topic
      const topic = await VocabularyTopic.findOne({ slug: topicSlug });
      
      if (topic) {
        // Add words to this topic
        const wordsWithTopicId = words.map(word => ({
          ...word,
          topicId: topic._id,
          language: 'en'
        }));

        await VocabularyWord.insertMany(wordsWithTopicId);
        
        // Update topic word count
        const currentWordCount = await VocabularyWord.countDocuments({ 
          topicId: topic._id, 
          isActive: true 
        });
        
        await VocabularyTopic.findByIdAndUpdate(
          topic._id,
          { wordCount: currentWordCount }
        );
        
        totalWordsAdded += words.length;
        console.log(`Added ${words.length} more words to topic: ${topic.name} (Total: ${currentWordCount})`);
      } else {
        console.log(`Topic not found: ${topicSlug}`);
      }
    }

    console.log(`\n✅ Additional vocabulary added!`);
    console.log(`📝 Total new words added: ${totalWordsAdded}`);

  } catch (error) {
    console.error('Error adding vocabulary:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

addMoreVocabulary();

import Head from 'next/head';
import { useLanguage } from '@/contexts/LanguageContext';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

export default function SEO({
  title,
  description,
  keywords,
  image = '/logo.svg',
  url = 'inenglish.io.vn',
  type = 'website'
}: SEOProps) {
  const { language } = useLanguage();

  const seoData = {
    vi: {
      defaultTitle: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Thông Minh Cho Người Việt',
      defaultDescription: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành riêng cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. <PERSON><PERSON><PERSON><PERSON> thi TOEIC, IELTS thành công. Nền tảng được hỗ trợ bởi quảng cáo để duy trì miễn phí.',
      defaultKeywords: 'học tiếng anh, học tiếng anh miễn phí, học tiếng anh online, AI học tiếng anh, trắc nghiệm tiếng anh, bài học tiếng anh, từ vựng tiếng anh, ngữ pháp tiếng anh, luyện nói tiếng anh, luyện thi TOEIC, luyện thi IELTS, phát âm tiếng anh, giao tiếp tiếng anh, khóa học tiếng anh miễn phí, học tiếng anh cho người Việt, ứng dụng học tiếng anh, nền tảng học tiếng anh Việt Nam, học tiếng anh cơ bản, học tiếng anh nâng cao'
    },
    en: {
      defaultTitle: 'Infinite English - Learn English Free with AI for Vietnamese',
      defaultDescription: 'Free English learning platform designed for Vietnamese learners with unlimited AI-generated questions. Practice multiple choice by level, track your learning progress. Ad-supported to keep the platform free.',
      defaultKeywords: 'learn english, free english learning, AI english learning, english quiz, english test, english vocabulary, english grammar, TOEIC, IELTS, english for vietnamese, vietnamese english learning, english learning platform'
    },
    zh: {
      defaultTitle: 'Infinite English - 免费AI英语学习平台（越南版）',
      defaultDescription: '专为越南学习者设计的免费英语学习平台，提供AI生成的无限题库。按级别练习选择题，跟踪学习进度。通过广告支持保持平台免费。',
      defaultKeywords: '学英语, 免费学英语, AI英语学习, 英语测验, 英语考试, 英语词汇, 英语语法, 托业, 雅思, 越南英语学习, 英语学习平台'
    }
  };

  const currentSEO = seoData[language];
  const finalTitle = title || currentSEO.defaultTitle;
  const finalDescription = description || currentSEO.defaultDescription;
  const finalKeywords = keywords || currentSEO.defaultKeywords;

  return (
    <Head>
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      
      {/* Open Graph */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:locale" content={language === 'vi' ? 'vi_VN' : language === 'zh' ? 'zh_CN' : 'en_US'} />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={image} />
      
      {/* Additional SEO */}
      <meta name="robots" content="index, follow" />
      <meta name="author" content="Infinite English" />
      <meta name="monetization" content="advertising" />
      <meta name="ads-supported" content="true" />
      <meta name="content-rating" content="general" />
      <meta name="target-audience" content="vietnamese learners" />
      <link rel="canonical" href={url} />

      {/* Language alternatives - Vietnamese priority */}
      <link rel="alternate" hrefLang="vi" href="https://inenglish.io.vn" />
      <link rel="alternate" hrefLang="en" href="https://inenglish.io.vn?lang=en" />
      <link rel="alternate" hrefLang="zh" href="https://inenglish.io.vn?lang=zh" />
      <link rel="alternate" hrefLang="x-default" href="https://inenglish.io.vn" />

      {/* AdSense optimization */}
      <meta name="google-adsense-account" content={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID} />
      <meta name="google-adsense-platform-account" content={process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID} />
    </Head>
  );
}

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, Level } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import AdSense from '@/components/AdSense';
import { VocabularyTopicsStructuredData } from '@/components/StructuredData';

interface Props {
  initialTopics: VocabularyTopic[];
}

export default function VocabularyServerPage({ initialTopics }: Props) {
  const router = useRouter();
  const { language } = useLanguage();
  const [topics] = useState<VocabularyTopic[]>(initialTopics);
  const [loading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedLevel] = useState<Level | 'all'>('all');

  // Handle level selection by navigating to appropriate URL
  const handleLevelChange = (level: Level | 'all') => {
    if (level === 'all') {
      router.push('/vocabulary');
    } else {
      router.push(`/vocabulary/${level}`);
    }
  };

  const handleTopicClick = (slug: string) => {
    router.push(`/vocabulary/${slug}`);
  };

  const getLevelText = (level: Level) => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getPageTitle = () => {
    switch (language) {
      case 'vi':
        return 'Từ Vựng Theo Chủ Đề';
      case 'zh':
        return '主题词汇';
      default:
        return 'Vocabulary Topics';
    }
  };

  const getPageDescription = () => {
    switch (language) {
      case 'vi':
        return 'Khám phá từ vựng tiếng Anh theo các chủ đề khác nhau. Học từ mới với định nghĩa, ví dụ và hướng dẫn phát âm.';
      case 'zh':
        return '按主题探索英语词汇。通过定义、例句和发音指导学习新单词。';
      default:
        return 'Explore English vocabulary by topics. Learn new words with definitions, examples, and pronunciation guides.';
    }
  };

  const getLevelDisplayText = (level: Level | 'all') => {
    if (level === 'all') {
      return language === 'vi' ? 'Tất cả' : language === 'zh' ? '全部' : 'All';
    }
    return getLevelText(level);
  };

  if (loading && topics.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <>
      <VocabularyTopicsStructuredData topics={topics} language={language} />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {getPageTitle()}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
          </div>

          {/* Level Filter */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-xl p-2 shadow-lg">
              <div className="flex space-x-2">
                {(['all', 'beginner', 'intermediate', 'advanced'] as const).map((level) => (
                  <button
                    key={level}
                    onClick={() => handleLevelChange(level)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedLevel === level
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {getLevelDisplayText(level)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense adSlot="vocabulary-main" />
          </div>

          {/* Loading overlay for filter changes */}
          {loading && (
            <div className="relative">
              <div className="absolute inset-0 bg-white bg-opacity-75 z-10 flex items-center justify-center">
                <LoadingSpinner />
              </div>
            </div>
          )}

          {/* Topics Grid */}
          {topics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {topics.map((topic) => (
                <div
                  key={topic._id}
                  onClick={() => handleTopicClick(topic.slug)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                      style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                    >
                      {topic.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900 mb-1">
                        {topic.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          topic.level === 'beginner' ? 'bg-green-100 text-green-700' :
                          topic.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {getLevelText(topic.level)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {topic.wordCount} {language === 'vi' ? 'từ' : language === 'zh' ? '词' : 'words'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm line-clamp-2">
                    {topic.description}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {language === 'vi' ? 'Không tìm thấy chủ đề nào' : 
                 language === 'zh' ? '未找到任何主题' : 
                 'No topics found'}
              </h3>
              <p className="text-gray-500">
                {language === 'vi' ? 'Thử thay đổi bộ lọc hoặc quay lại sau.' : 
                 language === 'zh' ? '尝试更改筛选器或稍后再试。' : 
                 'Try changing the filter or come back later.'}
              </p>
            </div>
          )}
        </div>
      </div>

      <Footer />
      
        {showAuthModal && (
          <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
        )}
      </div>
    </>
  );
}

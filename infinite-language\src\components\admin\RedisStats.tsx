'use client';

import { useState, useEffect } from 'react';

interface RedisStats {
  totalQuestions: number;
  oldestQuestion: number | null;
  oldestQuestionAge: number | null;
}

export default function RedisStats() {
  const [stats, setStats] = useState<RedisStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/redis?action=stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
      } else {
        setError(data.error || 'Failed to fetch stats');
      }
    } catch (err) {
      setError('Network error');
      console.error('Error fetching Redis stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const runCleanup = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/redis?action=cleanup');
      const data = await response.json();
      
      if (data.success) {
        alert(data.message);
        // Refresh stats after cleanup
        await fetchStats();
      } else {
        setError(data.error || 'Cleanup failed');
      }
    } catch (err) {
      setError('Network error during cleanup');
      console.error('Error running cleanup:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-md mx-auto">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Redis Question Storage</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading...</p>
        </div>
      ) : stats ? (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 p-3 rounded">
              <p className="text-sm text-blue-600 font-medium">Total Questions</p>
              <p className="text-2xl font-bold text-blue-800">{stats.totalQuestions}</p>
            </div>
            
            <div className="bg-green-50 p-3 rounded">
              <p className="text-sm text-green-600 font-medium">Oldest Question</p>
              <p className="text-lg font-bold text-green-800">
                {stats.oldestQuestionAge !== null 
                  ? `${stats.oldestQuestionAge}m ago`
                  : 'None'
                }
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={fetchStats}
              disabled={loading}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Refresh
            </button>
            
            <button
              onClick={runCleanup}
              disabled={loading}
              className="flex-1 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
            >
              Cleanup
            </button>
          </div>
          
          <div className="text-xs text-gray-500 mt-4">
            <p>• Questions auto-expire after 1 hour</p>
            <p>• Automatic cleanup runs every 30 minutes</p>
            <p>• Fallback to memory if Redis unavailable</p>
          </div>
        </div>
      ) : null}
    </div>
  );
}

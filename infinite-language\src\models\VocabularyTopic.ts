import mongoose, { Document, Schema } from 'mongoose';
import { Language, Level } from '@/types';

export interface IVocabularyTopic extends Document {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  level: Level;
  language: Language;
  wordCount: number;
  isActive: boolean;
  order: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: Date;
  updatedAt: Date;
}

const VocabularyTopicSchema = new Schema<IVocabularyTopic>({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  icon: {
    type: String,
    required: true,
    default: '📚',
  },
  color: {
    type: String,
    required: true,
    default: '#3B82F6',
  },
  level: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true,
  },
  language: {
    type: String,
    enum: ['en', 'vi', 'zh'],
    required: true,
    default: 'en',
  },
  wordCount: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  order: {
    type: Number,
    default: 0,
  },
  seoTitle: {
    type: String,
    trim: true,
  },
  seoDescription: {
    type: String,
    trim: true,
  },
  seoKeywords: [{
    type: String,
    trim: true,
  }],
}, {
  timestamps: true,
});

// Indexes for efficient queries (slug already has unique index from schema)
VocabularyTopicSchema.index({ level: 1, language: 1 });
VocabularyTopicSchema.index({ isActive: 1, order: 1 });
VocabularyTopicSchema.index({ language: 1, isActive: 1 });

export default mongoose.models.VocabularyTopic || mongoose.model<IVocabularyTopic>('VocabularyTopic', VocabularyTopicSchema);

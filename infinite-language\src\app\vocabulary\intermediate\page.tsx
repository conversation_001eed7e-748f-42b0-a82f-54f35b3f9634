import { Metadata } from 'next';
import { headers } from 'next/headers';
import VocabularyLevelServerPage from '@/components/VocabularyLevelServerPage';
import { VocabularyTopic } from '@/types';
import connectDB from '@/lib/mongodb';
import VocabularyTopicModel from '@/models/VocabularyTopic';

export const metadata: Metadata = {
  title: 'Intermediate Vocabulary - Infinite English',
  description: 'Expand your English vocabulary with intermediate-level words, phrases, and expressions. Perfect for improving your English fluency.',
  keywords: 'intermediate vocabulary, English vocabulary practice, intermediate English words, vocabulary building, English fluency',
  openGraph: {
    title: 'Intermediate Vocabulary - Infinite English',
    description: 'Build your English vocabulary with intermediate-level words and phrases.',
    type: 'website',
  },
};

async function getIntermediateTopics(language: string = 'vi'): Promise<VocabularyTopic[]> {
  try {
    await connectDB();

    const topics = await VocabularyTopicModel
      .find({
        language,
        level: 'intermediate',
        isActive: true
      })
      .sort({ order: 1, createdAt: -1 })
      .limit(50)
      .lean();

    return topics.map(topic => ({
      ...topic,
      _id: topic._id.toString(),
      createdAt: topic.createdAt.toISOString(),
      updatedAt: topic.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching intermediate vocabulary topics:', error);
    return [];
  }
}

export default async function IntermediateVocabularyPage() {
  // Get language from headers or default to Vietnamese
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const language = acceptLanguage.includes('zh') ? 'zh' :
                  acceptLanguage.includes('en') ? 'en' : 'vi';

  const topics = await getIntermediateTopics(language);

  return <VocabularyLevelServerPage level="intermediate" initialTopics={topics} />;
}

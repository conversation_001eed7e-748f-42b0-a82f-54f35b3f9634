import mongoose, { Document, Schema } from 'mongoose';
import { Course, Level, Language } from '@/types';

export interface ICourse extends Omit<Course, 'id'>, Document {
  _id: string;
}

const CourseSchema = new Schema<ICourse>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  level: { 
    type: String, 
    required: true,
    enum: ['beginner', 'intermediate', 'advanced']
  },
  language: { 
    type: String, 
    required: true,
    enum: ['en', 'vi', 'zh']
  },
  lessons: [{ type: String, required: true }], // lesson IDs in order
  estimatedDuration: { type: Number, required: true }, // total minutes
  difficulty: { type: Number, required: true, min: 1, max: 5 },
  category: { type: String, required: true },
  tags: [{ type: String }],
  thumbnail: { type: String },
  isPublished: { type: Boolean, default: false }
}, {
  timestamps: true
});

// Indexes for better performance
CourseSchema.index({ level: 1, language: 1 });
CourseSchema.index({ category: 1 });
CourseSchema.index({ tags: 1 });
CourseSchema.index({ isPublished: 1 });

export default mongoose.models.Course || mongoose.model<ICourse>('Course', CourseSchema);

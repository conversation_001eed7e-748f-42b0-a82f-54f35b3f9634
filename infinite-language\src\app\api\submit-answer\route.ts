import { NextRequest, NextResponse } from 'next/server';
import { questionStore } from '@/lib/questionStore';

export async function POST(request: NextRequest) {
  console.log('POST /api/submit-answer called');
  
  try {
    const body = await request.json();
    console.log('Request body:', body);
    
    const { questionId, userAnswer } = body;

    if (!questionId || userAnswer === undefined) {
      console.log('Missing questionId or userAnswer');
      return NextResponse.json(
        { success: false, error: 'Missing questionId or userAnswer' },
        { status: 400 }
      );
    }

    // Get the stored question data from Redis and delete it immediately (one-time use)
    const questionData = await questionStore.getAndDelete(questionId);
    console.log('Question data retrieved and deleted from store:', questionData);

    if (!questionData) {
      console.log('Question not found in store');
      return NextResponse.json(
        { success: false, error: 'Question not found or expired' },
        { status: 404 }
      );
    }

    const isCorrect = userAnswer === questionData.correctAnswer;
    console.log('Answer check:', { userAnswer, correctAnswer: questionData.correctAnswer, isCorrect });

    return NextResponse.json({
      success: true,
      result: {
        isCorrect,
        correctAnswer: questionData.correctAnswer,
        explanation: questionData.explanation,
        level: questionData.level,
        language: questionData.language
      }
    });

  } catch (error) {
    console.error('Error submitting answer:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit answer' },
      { status: 500 }
    );
  }
}

export async function GET() {
  console.log('GET /api/submit-answer called');
  return NextResponse.json({ message: 'Submit answer API is working' });
}

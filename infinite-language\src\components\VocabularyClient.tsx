'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, Level } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import AdSense from '@/components/AdSense';

export default function VocabularyClient() {
  const router = useRouter();
  const { t, language } = useLanguage();
  const [topics, setTopics] = useState<VocabularyTopic[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<Level | 'all'>('all');

  useEffect(() => {
    fetchTopics();
  }, [language, selectedLevel]);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        language,
        limit: '50',
      });

      if (selectedLevel !== 'all') {
        params.append('level', selectedLevel);
      }

      const response = await fetch(`/api/vocabulary/topics?${params}`);
      const data = await response.json();

      if (data.success) {
        setTopics(data.data.topics);
      } else {
        console.error('Failed to fetch topics:', data.error);
      }
    } catch (error) {
      console.error('Error fetching topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTopicClick = (slug: string) => {
    router.push(`/vocabulary/${slug}`);
  };

  const getLevelText = (level: Level) => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getPageTitle = () => {
    switch (language) {
      case 'vi':
        return 'Từ Vựng Theo Chủ Đề';
      case 'zh':
        return '主题词汇';
      default:
        return 'Vocabulary Topics';
    }
  };

  const getPageDescription = () => {
    switch (language) {
      case 'vi':
        return 'Khám phá từ vựng tiếng Anh theo các chủ đề khác nhau. Học từ mới với định nghĩa, ví dụ và hướng dẫn phát âm.';
      case 'zh':
        return '按主题探索英语词汇。通过定义、例句和发音指导学习新单词。';
      default:
        return 'Explore English vocabulary by topics. Learn new words with definitions, examples, and pronunciation guides.';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {getPageTitle()}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
          </div>

          {/* Level Filter */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-xl p-2 shadow-lg">
              <div className="flex space-x-2">
                {(['all', 'beginner', 'intermediate', 'advanced'] as const).map((level) => (
                  <button
                    key={level}
                    onClick={() => setSelectedLevel(level)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedLevel === level
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {level === 'all' 
                      ? (language === 'vi' ? 'Tất cả' : language === 'zh' ? '全部' : 'All')
                      : getLevelText(level as Level)
                    }
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense />
          </div>

          {/* Topics Grid */}
          {topics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {topics.map((topic) => (
                <div
                  key={topic._id}
                  onClick={() => handleTopicClick(topic.slug)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                      style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                    >
                      {topic.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900 mb-1">
                        {topic.name}
                      </h3>
                      <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        {getLevelText(topic.level)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {topic.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {topic.wordCount} {language === 'vi' ? 'từ' : language === 'zh' ? '个词' : 'words'}
                    </span>
                    <div className="text-blue-500 text-sm font-medium">
                      {language === 'vi' ? 'Học ngay →' : language === 'zh' ? '立即学习 →' : 'Learn now →'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {language === 'vi' ? 'Chưa có chủ đề nào' : language === 'zh' ? '暂无主题' : 'No topics available'}
              </h3>
              <p className="text-gray-600">
                {language === 'vi' 
                  ? 'Các chủ đề từ vựng sẽ được cập nhật sớm.' 
                  : language === 'zh' 
                  ? '词汇主题即将更新。' 
                  : 'Vocabulary topics will be available soon.'
                }
              </p>
            </div>
          )}
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      
      <Footer />
    </div>
  );
}

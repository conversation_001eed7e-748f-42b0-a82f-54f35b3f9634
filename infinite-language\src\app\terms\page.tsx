'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function TermsPage() {
  const { t, language } = useLanguage();

  const getContent = () => {
    switch (language) {
      case 'vi':
        return {
          title: "Điều khoản sử dụng",
          subtitle: "Vui lòng đọc kỹ các điều khoản và điều kiện sử dụng dịch vụ của chúng tôi",
          lastUpdated: "Cập nhật lần cuối: 1 tháng 1, 2025",
          sections: [
            {
              title: "1. Chấp nhận điều khoản",
              content: "Bằng việc truy cập và sử dụng trang web Infinite English, bạn đồng ý tuân thủ và bị ràng buộc bởi các điều khoản và điều kiện sử dụng này. Nếu bạn không đồng ý với bất kỳ phần nào của các điều kho<PERSON> này, bạn không được phép sử dụng dịch vụ của chúng tôi."
            },
            {
              title: "2. Mô tả dịch vụ",
              content: "Infinite English cung cấp nền tảng học tiếng Anh trực tuyến sử dụng công nghệ AI để tạo ra các câu hỏi, bài học và nội dung học tập cá nhân hóa. Dịch vụ của chúng tôi bao gồm nhưng không giới hạn ở: câu hỏi trắc nghiệm, bài học AI, bài đọc hiểu, và gia sư AI."
            },
            {
              title: "3. Tài khoản người dùng",
              content: "Để sử dụng một số tính năng của dịch vụ, bạn có thể cần tạo tài khoản. Bạn có trách nhiệm duy trì tính bảo mật của tài khoản và mật khẩu của mình. Bạn đồng ý chấp nhận trách nhiệm cho tất cả các hoạt động xảy ra dưới tài khoản của bạn."
            },
            {
              title: "4. Quyền sở hữu trí tuệ",
              content: "Tất cả nội dung trên trang web Infinite English, bao gồm văn bản, hình ảnh, logo, và phần mềm, đều thuộc sở hữu của chúng tôi hoặc các bên cấp phép của chúng tôi và được bảo vệ bởi luật bản quyền và các luật sở hữu trí tuệ khác."
            },
            {
              title: "5. Hành vi người dùng",
              content: "Bạn đồng ý không sử dụng dịch vụ để:\n• Vi phạm bất kỳ luật pháp nào\n• Gửi nội dung có hại, bôi nhọ, hoặc không phù hợp\n• Can thiệp vào hoạt động của dịch vụ\n• Thu thập thông tin người dùng khác mà không có sự đồng ý\n• Sử dụng dịch vụ cho mục đích thương mại mà không có sự cho phép"
            },
            {
              title: "6. Miễn trừ trách nhiệm",
              content: "Dịch vụ được cung cấp \"như hiện tại\" và \"như có sẵn\". Chúng tôi không đảm bảo rằng dịch vụ sẽ không bị gián đoạn, không có lỗi, hoặc hoàn toàn an toàn. Chúng tôi không chịu trách nhiệm về bất kỳ thiệt hại nào phát sinh từ việc sử dụng dịch vụ."
            },
            {
              title: "7. Thay đổi điều khoản",
              content: "Chúng tôi có quyền thay đổi các điều khoản này bất kỳ lúc nào. Các thay đổi sẽ có hiệu lực ngay khi được đăng tải trên trang web. Việc tiếp tục sử dụng dịch vụ sau khi có thay đổi đồng nghĩa với việc bạn chấp nhận các điều khoản mới."
            },
            {
              title: "8. Liên hệ",
              content: "Nếu bạn có bất kỳ câu hỏi nào về các điều khoản này, vui lòng liên hệ với chúng tôi qua email: <EMAIL>"
            }
          ]
        };
      case 'zh':
        return {
          title: "服务条款",
          subtitle: "请仔细阅读我们服务的条款和条件",
          lastUpdated: "最后更新：2025年1月1日",
          sections: [
            {
              title: "1. 接受条款",
              content: "通过访问和使用 Infinite English 网站，您同意遵守并受这些使用条款和条件的约束。如果您不同意这些条款的任何部分，您不得使用我们的服务。"
            },
            {
              title: "2. 服务描述",
              content: "Infinite English 提供使用AI技术创建个性化问题、课程和学习内容的在线英语学习平台。我们的服务包括但不限于：选择题、AI课程、阅读理解和AI导师。"
            },
            {
              title: "3. 用户账户",
              content: "要使用服务的某些功能，您可能需要创建账户。您有责任维护账户和密码的安全性。您同意对在您账户下发生的所有活动承担责任。"
            },
            {
              title: "4. 知识产权",
              content: "Infinite English 网站上的所有内容，包括文本、图像、徽标和软件，均归我们或我们的许可方所有，并受版权法和其他知识产权法保护。"
            },
            {
              title: "5. 用户行为",
              content: "您同意不使用服务来：\n• 违反任何法律\n• 发送有害、诽谤或不当内容\n• 干扰服务的运行\n• 未经同意收集其他用户信息\n• 未经许可将服务用于商业目的"
            },
            {
              title: "6. 免责声明",
              content: "服务按\"现状\"和\"可用\"基础提供。我们不保证服务不会中断、无错误或完全安全。我们不对因使用服务而产生的任何损害承担责任。"
            },
            {
              title: "7. 条款变更",
              content: "我们有权随时更改这些条款。更改将在网站上发布后立即生效。在更改后继续使用服务即表示您接受新条款。"
            },
            {
              title: "8. 联系我们",
              content: "如果您对这些条款有任何疑问，请通过电子邮件联系我们：<EMAIL>"
            }
          ]
        };
      default:
        return {
          title: "Terms of Service",
          subtitle: "Please read these terms and conditions carefully before using our service",
          lastUpdated: "Last updated: January 1, 2025",
          sections: [
            {
              title: "1. Acceptance of Terms",
              content: "By accessing and using the Infinite English website, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any part of these terms, you may not use our service."
            },
            {
              title: "2. Description of Service",
              content: "Infinite English provides an online English learning platform that uses AI technology to create personalized questions, lessons, and learning content. Our services include but are not limited to: multiple choice questions, AI lessons, reading comprehension, and AI tutoring."
            },
            {
              title: "3. User Accounts",
              content: "To use certain features of the service, you may be required to create an account. You are responsible for maintaining the confidentiality of your account and password. You agree to accept responsibility for all activities that occur under your account."
            },
            {
              title: "4. Intellectual Property",
              content: "All content on the Infinite English website, including text, images, logos, and software, is owned by us or our licensors and is protected by copyright and other intellectual property laws."
            },
            {
              title: "5. User Conduct",
              content: "You agree not to use the service to:\n• Violate any applicable laws\n• Post harmful, defamatory, or inappropriate content\n• Interfere with the operation of the service\n• Collect information about other users without consent\n• Use the service for commercial purposes without permission"
            },
            {
              title: "6. Disclaimer",
              content: "The service is provided \"as is\" and \"as available\" basis. We do not guarantee that the service will be uninterrupted, error-free, or completely secure. We are not liable for any damages arising from the use of the service."
            },
            {
              title: "7. Changes to Terms",
              content: "We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting on the website. Continued use of the service after changes constitutes acceptance of the new terms."
            },
            {
              title: "8. Contact Us",
              content: "If you have any questions about these Terms, please contact us at: <EMAIL>"
            }
          ]
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {content.title}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
              {content.subtitle}
            </p>
            <p className="text-sm text-gray-500">
              {content.lastUpdated}
            </p>
          </div>

          {/* Content */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="space-y-8">
              {content.sections.map((section, index) => (
                <div key={index}>
                  <h2 className="text-xl font-bold text-gray-900 mb-4">
                    {section.title}
                  </h2>
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                    {section.content}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-12 text-center">
            <div className="bg-blue-50 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {language === 'vi' ? 'Có câu hỏi?' : 
                 language === 'zh' ? '有问题吗？' : 
                 'Questions?'}
              </h3>
              <p className="text-gray-600 mb-6">
                {language === 'vi' ? 'Nếu bạn có bất kỳ câu hỏi nào về điều khoản sử dụng, đừng ngần ngại liên hệ với chúng tôi.' :
                 language === 'zh' ? '如果您对服务条款有任何疑问，请随时联系我们。' :
                 'If you have any questions about these terms of service, don\'t hesitate to contact us.'}
              </p>
              <a
                href="/contact"
                className="inline-block bg-blue-600 text-white font-semibold px-8 py-3 rounded-xl hover:bg-blue-700 transition-colors"
              >
                {language === 'vi' ? 'Liên hệ với chúng tôi' :
                 language === 'zh' ? '联系我们' :
                 'Contact Us'}
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}

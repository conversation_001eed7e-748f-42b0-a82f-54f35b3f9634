import { Metadata } from 'next';
import LessonsClient from '@/components/LessonsClient';

export const metadata: Metadata = {
  title: 'AI English Lessons - Infinite English',
  description: 'Create personalized English lessons with AI. Choose your topic, level, and lesson type to generate comprehensive learning content with exercises and explanations.',
  keywords: 'AI lessons, English lessons, personalized learning, grammar lessons, vocabulary lessons, conversation practice, AI-generated content',
  openGraph: {
    title: 'AI English Lessons - Infinite English',
    description: 'Create personalized English lessons with AI for any topic and level.',
    type: 'website',
  },
};

export default function LessonsPage() {
  return <LessonsClient />;
}

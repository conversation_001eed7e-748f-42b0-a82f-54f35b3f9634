'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { VocabularyTopic, Level } from '@/types';

interface Props {
  initialTopics: VocabularyTopic[];
}

export default function VocabularySimple({ initialTopics }: Props) {
  const router = useRouter();
  const [topics] = useState<VocabularyTopic[]>(initialTopics);
  const [selectedLevel] = useState<Level | 'all'>('all');

  // Handle level selection by navigating to appropriate URL
  const handleLevelChange = (level: Level | 'all') => {
    if (level === 'all') {
      router.push('/vocabulary');
    } else {
      router.push(`/vocabulary/${level}`);
    }
  };

  const handleTopicClick = (slug: string) => {
    router.push(`/vocabulary/${slug}`);
  };

  const getLevelText = (level: Level) => {
    switch (level) {
      case 'beginner':
        return '<PERSON><PERSON> bản';
      case 'intermediate':
        return 'Trung cấp';
      case 'advanced':
        return 'Nâng cao';
      default:
        return level;
    }
  };

  const getLevelDisplayText = (level: Level | 'all') => {
    if (level === 'all') {
      return 'Tất cả';
    }
    return getLevelText(level);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Từ Vựng Theo Chủ Đề
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Khám phá từ vựng tiếng Anh theo các chủ đề khác nhau. Học từ mới với định nghĩa, ví dụ và hướng dẫn phát âm.
            </p>
          </div>

          {/* Level Filter */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-xl p-2 shadow-lg">
              <div className="flex space-x-2">
                {(['all', 'beginner', 'intermediate', 'advanced'] as const).map((level) => (
                  <button
                    key={level}
                    onClick={() => handleLevelChange(level)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedLevel === level
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {getLevelDisplayText(level)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Topics Grid */}
          {topics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {topics.map((topic) => (
                <div
                  key={topic._id}
                  onClick={() => handleTopicClick(topic.slug)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                >
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                      style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                    >
                      {topic.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900 mb-1">
                        {topic.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          topic.level === 'beginner' ? 'bg-green-100 text-green-700' :
                          topic.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {getLevelText(topic.level)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {topic.wordCount} từ
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm line-clamp-2">
                    {topic.description}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📚</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                Không tìm thấy chủ đề nào
              </h3>
              <p className="text-gray-500">
                Thử thay đổi bộ lọc hoặc quay lại sau.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

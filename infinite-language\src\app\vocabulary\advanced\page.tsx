import { Metadata } from 'next';
import { headers } from 'next/headers';
import VocabularyLevelServerPage from '@/components/VocabularyLevelServerPage';
import { VocabularyTopic } from '@/types';
import connectDB from '@/lib/mongodb';
import VocabularyTopicModel from '@/models/VocabularyTopic';

export const metadata: Metadata = {
  title: 'Advanced Vocabulary - Infinite English',
  description: 'Master advanced English vocabulary with sophisticated words, academic terms, and professional expressions for fluent communication.',
  keywords: 'advanced vocabulary, sophisticated English words, academic vocabulary, professional English, advanced English terms',
  openGraph: {
    title: 'Advanced Vocabulary - Infinite English',
    description: 'Master advanced English vocabulary for professional and academic success.',
    type: 'website',
  },
};

async function getAdvancedTopics(language: string = 'vi'): Promise<VocabularyTopic[]> {
  try {
    await connectDB();

    const topics = await VocabularyTopicModel
      .find({
        language,
        level: 'advanced',
        isActive: true
      })
      .sort({ order: 1, createdAt: -1 })
      .limit(50)
      .lean();

    return topics.map(topic => ({
      ...topic,
      _id: topic._id.toString(),
      createdAt: topic.createdAt.toISOString(),
      updatedAt: topic.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching advanced vocabulary topics:', error);
    return [];
  }
}

export default async function AdvancedVocabularyPage() {
  // Get language from headers or default to Vietnamese
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const language = acceptLanguage.includes('zh') ? 'zh' :
                  acceptLanguage.includes('en') ? 'en' : 'vi';

  const topics = await getAdvancedTopics(language);

  return <VocabularyLevelServerPage level="advanced" initialTopics={topics} />;
}

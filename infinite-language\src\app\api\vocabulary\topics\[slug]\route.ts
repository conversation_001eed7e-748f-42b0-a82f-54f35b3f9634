import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import VocabularyTopic from '@/models/VocabularyTopic';
import VocabularyWord from '@/models/VocabularyWord';

// GET - Get vocabulary topic by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB();

    const { slug } = params;
    const { searchParams } = new URL(request.url);
    const includeWords = searchParams.get('includeWords') === 'true';

    // Find topic by slug
    const topic = await VocabularyTopic.findOne({
      slug,
      isActive: true,
    }).lean();

    if (!topic) {
      return NextResponse.json(
        { success: false, error: 'Topic not found' },
        { status: 404 }
      );
    }

    let words = [];
    if (includeWords) {
      // Get words for this topic
      words = await VocabularyWord.find({
        topicId: topic._id,
        isActive: true,
      })
        .sort({ order: 1, createdAt: -1 })
        .lean();
    }

    return NextResponse.json({
      success: true,
      data: {
        topic,
        words: includeWords ? words : undefined,
      },
    });
  } catch (error) {
    console.error('Error fetching vocabulary topic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch vocabulary topic' },
      { status: 500 }
    );
  }
}

// PUT - Update vocabulary topic
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB();

    const { slug } = params;
    const body = await request.json();

    // Find and update topic
    const topic = await VocabularyTopic.findOneAndUpdate(
      { slug, isActive: true },
      { ...body, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!topic) {
      return NextResponse.json(
        { success: false, error: 'Topic not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: topic,
    });
  } catch (error) {
    console.error('Error updating vocabulary topic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update vocabulary topic' },
      { status: 500 }
    );
  }
}

// DELETE - Delete vocabulary topic
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB();

    const { slug } = params;

    // Soft delete by setting isActive to false
    const topic = await VocabularyTopic.findOneAndUpdate(
      { slug, isActive: true },
      { isActive: false, updatedAt: new Date() },
      { new: true }
    );

    if (!topic) {
      return NextResponse.json(
        { success: false, error: 'Topic not found' },
        { status: 404 }
      );
    }

    // Also soft delete all words in this topic
    await VocabularyWord.updateMany(
      { topicId: topic._id },
      { isActive: false, updatedAt: new Date() }
    );

    return NextResponse.json({
      success: true,
      message: 'Topic deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting vocabulary topic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete vocabulary topic' },
      { status: 500 }
    );
  }
}

import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IVocabularyProgress extends Document {
  _id: string;
  userId: Types.ObjectId;
  wordId: Types.ObjectId;
  topicId: Types.ObjectId;
  status: 'learning' | 'reviewing' | 'mastered';
  correctAttempts: number;
  totalAttempts: number;
  lastReviewedAt: Date;
  nextReviewAt: Date;
  masteredAt?: Date;
  studyStreak: number;
  timeSpent: number; // in seconds
  createdAt: Date;
  updatedAt: Date;
}

const VocabularyProgressSchema = new Schema<IVocabularyProgress>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  wordId: {
    type: Schema.Types.ObjectId,
    ref: 'VocabularyWord',
    required: true,
  },
  topicId: {
    type: Schema.Types.ObjectId,
    ref: 'VocabularyTopic',
    required: true,
  },
  status: {
    type: String,
    enum: ['learning', 'reviewing', 'mastered'],
    default: 'learning',
  },
  correctAttempts: {
    type: Number,
    default: 0,
  },
  totalAttempts: {
    type: Number,
    default: 0,
  },
  lastReviewedAt: {
    type: Date,
    default: Date.now,
  },
  nextReviewAt: {
    type: Date,
    default: Date.now,
  },
  masteredAt: {
    type: Date,
  },
  studyStreak: {
    type: Number,
    default: 0,
  },
  timeSpent: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Compound indexes for efficient queries
VocabularyProgressSchema.index({ userId: 1, topicId: 1 });
VocabularyProgressSchema.index({ userId: 1, wordId: 1 }, { unique: true });
VocabularyProgressSchema.index({ userId: 1, status: 1 });
VocabularyProgressSchema.index({ userId: 1, nextReviewAt: 1 });
VocabularyProgressSchema.index({ topicId: 1, status: 1 });

export default mongoose.models.VocabularyProgress || mongoose.model<IVocabularyProgress>('VocabularyProgress', VocabularyProgressSchema);

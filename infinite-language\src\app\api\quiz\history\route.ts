import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import QuizHistory from '@/models/QuizHistory';
import User from '@/models/User';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// GET - Retrieve quiz history for authenticated user
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const level = searchParams.get('level');
    const isCorrect = searchParams.get('isCorrect');

    // Build query
    const query: Record<string, unknown> = { userId: session.user.id };
    if (level) query.level = level;
    if (isCorrect !== null && isCorrect !== undefined) {
      query.isCorrect = isCorrect === 'true';
    }

    // Get total count
    const total = await QuizHistory.countDocuments(query);

    // Get paginated results
    const history = await QuizHistory.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    return NextResponse.json({
      success: true,
      data: {
        history,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });

  } catch (error) {
    console.error('Get quiz history error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Save quiz result for authenticated user
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Get session from NextAuth
    const session = await getServerSession(authOptions);

    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const {
      questionId,
      question,
      options,
      correctAnswer,
      userAnswer,
      explanation,
      level,
      language,
      timeSpent = 0,
    } = await request.json();

    // Validation
    if (!questionId || !question || !options || correctAnswer === undefined || userAnswer === undefined) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const isCorrect = userAnswer === correctAnswer;

    // Save quiz history
    const quizHistory = new QuizHistory({
      userId: session.user.id,
      questionId,
      question,
      options,
      correctAnswer,
      userAnswer,
      isCorrect,
      explanation,
      level,
      language,
      timeSpent,
    });

    await quizHistory.save();

    // Update user stats
    const user = await User.findById(session.user.id);
    if (user) {
      user.stats.totalQuestions += 1;
      if (isCorrect) {
        user.stats.correctAnswers += 1;
        user.stats.streakCount += 1;
      } else {
        user.stats.streakCount = 0;
      }
      
      // Update level-specific stats
      user.stats.levelStats[level].total += 1;
      if (isCorrect) {
        user.stats.levelStats[level].correct += 1;
      }
      
      user.stats.lastActiveDate = new Date();
      await user.save();
    }

    return NextResponse.json({
      success: true,
      data: quizHistory,
    });

  } catch (error) {
    console.error('Save quiz history error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

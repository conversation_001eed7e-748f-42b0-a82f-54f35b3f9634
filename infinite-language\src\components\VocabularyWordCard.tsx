'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyWord } from '@/types';

interface Props {
  word: VocabularyWord;
}

export default function VocabularyWordCard({ word }: Props) {
  const { language } = useLanguage();
  const [isFlipped, setIsFlipped] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const playPronunciation = () => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    
    // Use Web Speech API for pronunciation
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(word.word);
      utterance.lang = 'en-US';
      utterance.rate = 0.8;
      utterance.onend = () => setIsPlaying(false);
      speechSynthesis.speak(utterance);
    } else {
      // Fallback: just reset the playing state
      setTimeout(() => setIsPlaying(false), 1000);
    }
  };

  const getPartOfSpeechText = (pos: string) => {
    const translations: Record<string, Record<string, string>> = {
      noun: { vi: 'Danh từ', zh: '名词', en: 'Noun' },
      verb: { vi: 'Động từ', zh: '动词', en: 'Verb' },
      adjective: { vi: 'Tính từ', zh: '形容词', en: 'Adjective' },
      adverb: { vi: 'Trạng từ', zh: '副词', en: 'Adverb' },
      preposition: { vi: 'Giới từ', zh: '介词', en: 'Preposition' },
      conjunction: { vi: 'Liên từ', zh: '连词', en: 'Conjunction' },
      interjection: { vi: 'Thán từ', zh: '感叹词', en: 'Interjection' },
      pronoun: { vi: 'Đại từ', zh: '代词', en: 'Pronoun' },
    };
    
    return translations[pos]?.[language] || pos;
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty <= 2) return 'text-green-600 bg-green-100';
    if (difficulty <= 3) return 'text-yellow-600 bg-yellow-100';
    if (difficulty <= 4) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getDifficultyText = (difficulty: number) => {
    const texts: Record<string, string[]> = {
      vi: ['Rất dễ', 'Dễ', 'Trung bình', 'Khó', 'Rất khó'],
      zh: ['很容易', '容易', '中等', '困难', '很困难'],
      en: ['Very Easy', 'Easy', 'Medium', 'Hard', 'Very Hard'],
    };
    
    return texts[language]?.[difficulty - 1] || `Level ${difficulty}`;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
      <div className="p-6">
        {/* Word Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-2xl font-bold text-gray-900 capitalize">
                {word.word}
              </h3>
              <button
                onClick={playPronunciation}
                disabled={isPlaying}
                className={`p-2 rounded-full transition-all duration-200 ${
                  isPlaying 
                    ? 'bg-blue-200 text-blue-600' 
                    : 'bg-gray-100 hover:bg-blue-100 text-gray-600 hover:text-blue-600'
                }`}
                title={language === 'vi' ? 'Phát âm' : language === 'zh' ? '发音' : 'Pronunciation'}
              >
                {isPlaying ? '🔊' : '🔉'}
              </button>
            </div>
            
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-gray-600 font-mono text-sm">
                {word.phonetic}
              </span>
              <span className="text-gray-500">•</span>
              <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
                {getPartOfSpeechText(word.partOfSpeech)}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(word.difficulty)}`}>
                {getDifficultyText(word.difficulty)}
              </span>
              <div className="flex">
                {Array.from({ length: 5 }, (_, i) => (
                  <span
                    key={i}
                    className={`text-xs ${
                      i < word.frequency ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  >
                    ⭐
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Definitions */}
        <div className="space-y-3">
          {word.definitions.slice(0, isFlipped ? word.definitions.length : 2).map((def, index) => (
            <div key={index} className="border-l-4 border-blue-200 pl-4">
              <p className="text-gray-800 font-medium mb-1">
                {index + 1}. {def.meaning}
              </p>
              <p className="text-gray-600 text-sm italic">
                "{def.example}"
              </p>
              {def.translation && (
                <p className="text-blue-600 text-sm mt-1">
                  {language === 'vi' ? '🇻🇳' : language === 'zh' ? '🇨🇳' : '🌐'} {def.translation}
                </p>
              )}
            </div>
          ))}
        </div>

        {/* Show More/Less Button */}
        {word.definitions.length > 2 && (
          <button
            onClick={() => setIsFlipped(!isFlipped)}
            className="mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
          >
            {isFlipped 
              ? (language === 'vi' ? 'Thu gọn' : language === 'zh' ? '收起' : 'Show less')
              : (language === 'vi' ? `Xem thêm ${word.definitions.length - 2} định nghĩa` : 
                 language === 'zh' ? `查看更多 ${word.definitions.length - 2} 个定义` : 
                 `Show ${word.definitions.length - 2} more definitions`)
            }
          </button>
        )}

        {/* Additional Info (shown when flipped) */}
        {isFlipped && (
          <div className="mt-4 pt-4 border-t border-gray-100 space-y-3">
            {/* Synonyms */}
            {word.synonyms.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  {language === 'vi' ? 'Từ đồng nghĩa:' : language === 'zh' ? '同义词:' : 'Synonyms:'}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {word.synonyms.map((synonym, index) => (
                    <span
                      key={index}
                      className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full"
                    >
                      {synonym}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Antonyms */}
            {word.antonyms.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  {language === 'vi' ? 'Từ trái nghĩa:' : language === 'zh' ? '反义词:' : 'Antonyms:'}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {word.antonyms.map((antonym, index) => (
                    <span
                      key={index}
                      className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full"
                    >
                      {antonym}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Collocations */}
            {word.collocations.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  {language === 'vi' ? 'Cụm từ thường dùng:' : language === 'zh' ? '常用搭配:' : 'Common phrases:'}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {word.collocations.map((collocation, index) => (
                    <span
                      key={index}
                      className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full"
                    >
                      {collocation}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

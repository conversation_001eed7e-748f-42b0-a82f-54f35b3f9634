export type Level = 'beginner' | 'intermediate' | 'advanced';
export type Language = 'en' | 'vi' | 'zh';
export type LessonType = 'grammar' | 'vocabulary' | 'reading' | 'listening' | 'speaking' | 'writing';
export type ContentType = 'text' | 'audio' | 'video' | 'interactive' | 'quiz';

export interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer?: number; // Optional for client-side (not sent from API)
  explanation?: string; // Optional for client-side (not sent from API)
  level: Level;
}

export interface FullQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  level: Level;
}

export interface QuestionResult {
  isCorrect: boolean;
  correctAnswer: number;
  explanation: string;
  level: Level;
  language: Language;
}

// Lesson System Types
export interface LessonContent {
  id: string;
  type: ContentType;
  title: string;
  content: string;
  audioUrl?: string;
  videoUrl?: string;
  examples?: string[];
  exercises?: Exercise[];
  order: number;
}

export interface Exercise {
  id: string;
  type: 'multiple-choice' | 'fill-blank' | 'drag-drop' | 'matching' | 'writing';
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation: string;
  points: number;
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  type: LessonType;
  level: Level;
  language: Language;
  duration: number; // in minutes
  difficulty: number; // 1-5 scale
  prerequisites?: string[]; // lesson IDs
  objectives: string[];
  content: LessonContent[];
  totalPoints: number;
  tags: string[];
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface LessonProgress {
  userId: string;
  lessonId: string;
  completedContent: string[]; // content IDs
  score: number;
  timeSpent: number; // in minutes
  isCompleted: boolean;
  lastAccessedAt: Date;
  attempts: number;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  level: Level;
  language: Language;
  lessons: string[]; // lesson IDs in order
  estimatedDuration: number; // total minutes
  difficulty: number;
  category: string;
  tags: string[];
  thumbnail?: string;
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseProgress {
  userId: string;
  courseId: string;
  completedLessons: string[];
  currentLesson: string;
  overallScore: number;
  timeSpent: number;
  isCompleted: boolean;
  startedAt: Date;
  lastAccessedAt: Date;
}

// Vocabulary System Types
export interface VocabularyWord {
  id: string;
  word: string;
  pronunciation: string;
  partOfSpeech: string;
  definition: string;
  example: string;
  synonyms?: string[];
  antonyms?: string[];
  level: Level;
  category: string;
  frequency: number; // 1-5 scale (how common the word is)
  audioUrl?: string;
  imageUrl?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface VocabularyProgress {
  userId: string;
  wordId: string;
  familiarity: number; // 0-5 scale (how well user knows the word)
  correctAnswers: number;
  totalAttempts: number;
  lastReviewed: Date;
  nextReview: Date;
  streak: number;
  isLearned: boolean;
  difficulty: number; // calculated based on user performance
}

export interface FlashcardSet {
  id: string;
  title: string;
  description: string;
  words: string[]; // word IDs
  level: Level;
  category: string;
  isPublic: boolean;
  createdBy: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface WordGame {
  id: string;
  type: 'matching' | 'word-search' | 'crossword' | 'spelling' | 'definition-match';
  title: string;
  description: string;
  words: string[]; // word IDs
  level: Level;
  timeLimit?: number; // in seconds
  maxScore: number;
  instructions: string;
}

export interface GameResult {
  userId: string;
  gameId: string;
  score: number;
  timeSpent: number;
  correctAnswers: number;
  totalQuestions: number;
  completedAt: Date;
}

export interface QuizState {
  currentQuestion: Question | null;
  selectedAnswer: number | null;
  showResult: boolean;
  score: {
    correct: number;
    total: number;
  };
  level: Level | null;
  language: Language;
}

export interface ApiResponse {
  success: boolean;
  question?: Question;
  error?: string;
}

export interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

export interface User {
  id: string;
  email: string;
  username: string;
  preferredLanguage: Language;
  stats: {
    totalQuestions: number;
    correctAnswers: number;
    streakCount: number;
    lastActiveDate: string | Date;
    levelStats: {
      beginner: { total: number; correct: number };
      intermediate: { total: number; correct: number };
      advanced: { total: number; correct: number };
    };
  };
  createdAt?: string;
}

export interface AuthContextType {
  user: User | null;
  logout: () => Promise<void>;
  loading: boolean;
  checkAuth: () => Promise<void>;
}

export interface QuizHistoryItem {
  _id: string;
  questionId: string;
  question: string;
  options: string[];
  correctAnswer: number;
  userAnswer: number;
  isCorrect: boolean;
  explanation: string;
  level: Level;
  language: Language;
  timeSpent: number;
  createdAt: string;
}

// Vocabulary Types
export interface VocabularyTopic {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  level: Level;
  language: Language;
  wordCount: number;
  isActive: boolean;
  order: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface VocabularyWord {
  _id: string;
  topicId: string;
  word: string;
  pronunciation: string;
  phonetic: string;
  partOfSpeech: 'noun' | 'verb' | 'adjective' | 'adverb' | 'preposition' | 'conjunction' | 'interjection' | 'pronoun';
  level: Level;
  language: Language;
  definitions: Array<{
    meaning: string;
    example: string;
    translation?: string;
  }>;
  synonyms: string[];
  antonyms: string[];
  collocations: string[];
  frequency: number;
  difficulty: number;
  audioUrl?: string;
  imageUrl?: string;
  tags: string[];
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface VocabularyProgress {
  _id: string;
  userId: string;
  wordId: string;
  topicId: string;
  status: 'learning' | 'reviewing' | 'mastered';
  correctAttempts: number;
  totalAttempts: number;
  lastReviewedAt: string;
  nextReviewAt: string;
  masteredAt?: string;
  studyStreak: number;
  timeSpent: number;
  createdAt: string;
  updatedAt: string;
}

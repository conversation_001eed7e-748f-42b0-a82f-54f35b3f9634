import { Level, Language } from '@/types';
import { createClient } from 'redis';

// Redis client configuration
const redis = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 5000,
  },
});

// Handle Redis connection errors
redis.on('error', (err) => {
  console.error('Redis Client Error:', err);
});

// Connect to Redis
let isConnected = false;
async function connectRedis() {
  if (!isConnected) {
    try {
      await redis.connect();
      isConnected = true;
      console.log('Connected to Redis');
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      throw error;
    }
  }
}

// Question data interface
interface QuestionData {
  correctAnswer: number;
  explanation: string;
  level: Level;
  language: Language;
  createdAt: number;
}

// Redis-based question store with automatic cleanup
export const questionStore = {
  // Set question data with TTL (Time To Live)
  async set(questionId: string, data: Omit<QuestionData, 'createdAt'>): Promise<void> {
    try {
      await connectRedis();

      const questionData: QuestionData = {
        ...data,
        createdAt: Date.now()
      };

      // Store with 1 hour TTL (3600 seconds)
      await redis.setEx(
        `question:${questionId}`,
        3600,
        JSON.stringify(questionData)
      );
    } catch (error) {
      console.error('Error storing question in Redis:', error);
      // Fallback to memory storage if Redis fails
      memoryFallback.set(questionId, data);
    }
  },

  // Get question data
  async get(questionId: string): Promise<QuestionData | null> {
    try {
      await connectRedis();

      const data = await redis.get(`question:${questionId}`);
      if (data) {
        return JSON.parse(data) as QuestionData;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving question from Redis:', error);
      // Fallback to memory storage if Redis fails
      const fallbackData = memoryFallback.get(questionId);
      if (fallbackData) {
        return {
          ...fallbackData,
          createdAt: Date.now() // Add current timestamp for fallback data
        };
      }
      return null;
    }
  },

  // Get question data and immediately delete it (one-time use)
  async getAndDelete(questionId: string): Promise<QuestionData | null> {
    try {
      await connectRedis();

      const data = await redis.get(`question:${questionId}`);
      if (data) {
        // Delete the question immediately after retrieving
        await redis.del(`question:${questionId}`);
        console.log('Question retrieved and deleted from Redis:', questionId);
        return JSON.parse(data) as QuestionData;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving and deleting question from Redis:', error);
      // Fallback to memory storage if Redis fails
      const fallbackData = memoryFallback.get(questionId);
      if (fallbackData) {
        memoryFallback.delete(questionId); // Also delete from fallback
        return {
          ...fallbackData,
          createdAt: Date.now() // Add current timestamp for fallback data
        };
      }
      return null;
    }
  },

  // Delete question data
  async delete(questionId: string): Promise<void> {
    try {
      await connectRedis();
      await redis.del(`question:${questionId}`);
    } catch (error) {
      console.error('Error deleting question from Redis:', error);
      // Fallback to memory storage if Redis fails
      memoryFallback.delete(questionId);
    }
  },

  // Clean up expired questions (manual cleanup for additional safety)
  async cleanup(): Promise<number> {
    try {
      await connectRedis();

      const keys = await redis.keys('question:*');
      let deletedCount = 0;

      for (const key of keys) {
        const data = await redis.get(key);
        if (data) {
          const questionData = JSON.parse(data) as QuestionData;
          const now = Date.now();
          const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

          // Delete if older than 1 hour
          if (now - questionData.createdAt > oneHour) {
            await redis.del(key);
            deletedCount++;
          }
        }
      }

      console.log(`Cleaned up ${deletedCount} expired questions`);
      return deletedCount;
    } catch (error) {
      console.error('Error during cleanup:', error);
      return 0;
    }
  },

  // Get statistics
  async getStats(): Promise<{ totalQuestions: number; oldestQuestion: number | null }> {
    try {
      await connectRedis();

      const keys = await redis.keys('question:*');
      let oldestTimestamp: number | null = null;

      for (const key of keys) {
        const data = await redis.get(key);
        if (data) {
          const questionData = JSON.parse(data) as QuestionData;
          if (oldestTimestamp === null || questionData.createdAt < oldestTimestamp) {
            oldestTimestamp = questionData.createdAt;
          }
        }
      }

      return {
        totalQuestions: keys.length,
        oldestQuestion: oldestTimestamp
      };
    } catch (error) {
      console.error('Error getting stats:', error);
      return { totalQuestions: 0, oldestQuestion: null };
    }
  }
};

// Memory fallback for when Redis is unavailable
const memoryFallback = new Map<string, Omit<QuestionData, 'createdAt'>>();

// Periodic cleanup function (runs every 30 minutes)
if (typeof window === 'undefined') { // Only run on server side
  setInterval(async () => {
    try {
      await questionStore.cleanup();
    } catch (error) {
      console.error('Periodic cleanup failed:', error);
    }
  }, 30 * 60 * 1000); // 30 minutes
}

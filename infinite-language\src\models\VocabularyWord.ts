import mongoose, { Document, Schema, Types } from 'mongoose';
import { Language, Level } from '@/types';

export interface IVocabularyWord extends Document {
  _id: string;
  topicId: Types.ObjectId;
  word: string;
  pronunciation: string;
  phonetic: string;
  partOfSpeech: string;
  level: Level;
  language: Language;
  definitions: Array<{
    meaning: string;
    example: string;
    translation?: string;
  }>;
  synonyms: string[];
  antonyms: string[];
  collocations: string[];
  frequency: number; // 1-5 (how common the word is)
  difficulty: number; // 1-5 (how difficult the word is)
  audioUrl?: string;
  imageUrl?: string;
  tags: string[];
  isActive: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const VocabularyWordSchema = new Schema<IVocabularyWord>({
  topicId: {
    type: Schema.Types.ObjectId,
    ref: 'VocabularyTopic',
    required: true,
  },
  word: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
  },
  pronunciation: {
    type: String,
    required: true,
    trim: true,
  },
  phonetic: {
    type: String,
    required: true,
    trim: true,
  },
  partOfSpeech: {
    type: String,
    required: true,
    enum: ['noun', 'verb', 'adjective', 'adverb', 'preposition', 'conjunction', 'interjection', 'pronoun'],
  },
  level: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    required: true,
  },
  language: {
    type: String,
    enum: ['en', 'vi', 'zh'],
    required: true,
    default: 'en',
  },
  definitions: [{
    meaning: {
      type: String,
      required: true,
      trim: true,
    },
    example: {
      type: String,
      required: true,
      trim: true,
    },
    translation: {
      type: String,
      trim: true,
    },
  }],
  synonyms: [{
    type: String,
    trim: true,
  }],
  antonyms: [{
    type: String,
    trim: true,
  }],
  collocations: [{
    type: String,
    trim: true,
  }],
  frequency: {
    type: Number,
    min: 1,
    max: 5,
    default: 3,
  },
  difficulty: {
    type: Number,
    min: 1,
    max: 5,
    default: 3,
  },
  audioUrl: {
    type: String,
    trim: true,
  },
  imageUrl: {
    type: String,
    trim: true,
  },
  tags: [{
    type: String,
    trim: true,
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  order: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Indexes for efficient queries
VocabularyWordSchema.index({ topicId: 1, isActive: 1, order: 1 });
VocabularyWordSchema.index({ word: 1 });
VocabularyWordSchema.index({ level: 1, language: 1 });
VocabularyWordSchema.index({ partOfSpeech: 1 });
VocabularyWordSchema.index({ frequency: 1 });
VocabularyWordSchema.index({ difficulty: 1 });
VocabularyWordSchema.index({ tags: 1 });

export default mongoose.models.VocabularyWord || mongoose.model<IVocabularyWord>('VocabularyWord', VocabularyWordSchema);

import { Metadata } from 'next';
import GrammarClient from '@/components/GrammarClient';

export const metadata: Metadata = {
  title: 'English Grammar - Infinite English',
  description: 'Master English grammar with comprehensive lessons, rules, and practice exercises. Learn tenses, sentence structure, and grammar fundamentals.',
  keywords: 'English grammar, grammar rules, English tenses, grammar practice, sentence structure, grammar lessons',
  openGraph: {
    title: 'English Grammar - Infinite English',
    description: 'Master English grammar with comprehensive lessons and practice exercises.',
    type: 'website',
  },
};

export default function GrammarPage() {
  return <GrammarClient />;
}

'use client';

import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';

interface ErrorPageProps {
  title?: string;
  message?: string;
  showBackButton?: boolean;
  backHref?: string;
  backText?: string;
}

export default function ErrorPage({ 
  title, 
  message, 
  showBackButton = true,
  backHref = "/",
  backText 
}: ErrorPageProps) {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {title || t('failedToGenerate')}
          </h2>
          <p className="text-gray-600 mb-4">
            {message || t('questionGenerateError')}
          </p>
          {showBackButton && (
            <Link
              href={backHref}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {backText || t('backToHome')}
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { questionStore } from '@/lib/questionStore';

export async function POST(request: NextRequest) {
  console.log('POST /api/check-answer called');
  
  try {
    const body = await request.json();
    console.log('Request body:', body);
    
    const { questionId, userAnswer } = body;

    if (!questionId || userAnswer === undefined) {
      console.log('Missing questionId or userAnswer');
      return NextResponse.json(
        { success: false, error: 'Missing questionId or userAnswer' },
        { status: 400 }
      );
    }

    // Get the stored question data and delete it immediately (one-time use)
    const questionData = await questionStore.getAndDelete(questionId);
    console.log('Question data retrieved and deleted from store:', questionData);

    if (!questionData) {
      console.log('Question not found in store');
      return NextResponse.json(
        { success: false, error: 'Question not found or expired' },
        { status: 404 }
      );
    }

    const isCorrect = userAnswer === questionData.correctAnswer;
    console.log('Answer check:', { userAnswer, correctAnswer: questionData.correctAnswer, isCorrect });

    return NextResponse.json({
      success: true,
      result: {
        isCorrect,
        correctAnswer: questionData.correctAnswer,
        explanation: questionData.explanation,
        level: questionData.level,
        language: questionData.language
      }
    });

  } catch (error) {
    console.error('Error checking answer:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check answer' },
      { status: 500 }
    );
  }
}

export async function GET() {
  console.log('GET /api/check-answer called');
  return NextResponse.json({ message: 'Check answer API is working' });
}

import { VocabularyTopic, VocabularyWord } from '@/types';

interface StructuredDataProps {
  type: 'website' | 'course' | 'article' | 'educational-organization' | 'quiz';
  data: any;
}

interface VocabularyTopicsStructuredDataProps {
  topics: VocabularyTopic[];
  language: string;
}

interface VocabularyTopicStructuredDataProps {
  topic: VocabularyTopic;
  words: VocabularyWord[];
  language: string;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
    };

    switch (type) {
      case 'website':
        return {
          ...baseData,
          "@type": "WebSite",
          "name": "Infinite English",
          "description": "Học tiếng Anh với AI - Nền tảng học tiếng Anh miễn phí dành cho người Việt với bà<PERSON> h<PERSON>, tr<PERSON><PERSON> nghiệm và tài li<PERSON><PERSON> h<PERSON><PERSON> tập to<PERSON>n di<PERSON>n",
          "url": "https://inenglish.io.vn",
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": "https://inenglish.io.vn/search?q={search_term_string}"
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Infinite English",
            "logo": {
              "@type": "ImageObject",
              "url": "https://inenglish.io.vn/logo.png"
            }
          }
        };

      case 'educational-organization':
        return {
          ...baseData,
          "@type": "EducationalOrganization",
          "name": "Infinite English",
          "description": "Nền tảng học tiếng Anh với AI dành cho người Việt - Bài học toàn diện và tài liệu luyện tập miễn phí",
          "url": "https://inenglish.io.vn",
          "logo": "https://inenglish.io.vn/logo.png",
          "sameAs": [],
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "VN"
          },
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "VND",
            "availability": "https://schema.org/InStock"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "English Learning Courses",
            "itemListElement": [
              {
                "@type": "Course",
                "name": "Beginner English",
                "description": "Basic vocabulary and simple grammar for English beginners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Beginner",
                "courseMode": "Online",
                "isAccessibleForFree": true
              },
              {
                "@type": "Course", 
                "name": "Intermediate English",
                "description": "More complex sentences and vocabulary for intermediate learners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Intermediate",
                "courseMode": "Online",
                "isAccessibleForFree": true
              },
              {
                "@type": "Course",
                "name": "Advanced English",
                "description": "Complex grammar and advanced vocabulary for advanced learners",
                "provider": {
                  "@type": "Organization",
                  "name": "Infinite English"
                },
                "educationalLevel": "Advanced",
                "courseMode": "Online",
                "isAccessibleForFree": true
              }
            ]
          }
        };

      case 'course':
        return {
          ...baseData,
          "@type": "Course",
          "name": data.name,
          "description": data.description,
          "provider": {
            "@type": "Organization",
            "name": "Infinite English",
            "url": "https://inenglish.io.vn"
          },
          "educationalLevel": data.level,
          "courseMode": "Online",
          "isAccessibleForFree": true,
          "inLanguage": "vi",
          "availableLanguage": ["vi", "en", "zh"],
          "teaches": data.objectives || [],
          "timeRequired": data.duration ? `PT${data.duration}M` : undefined,
          "coursePrerequisites": data.prerequisites || [],
          "educationalCredentialAwarded": "Certificate of Completion"
        };

      case 'quiz':
        return {
          ...baseData,
          "@type": "Quiz",
          "name": data.title || "English Quiz",
          "description": data.description || "Interactive English quiz for Vietnamese learners",
          "educationalLevel": data.level || "beginner",
          "inLanguage": "vi",
          "isAccessibleForFree": true,
          "learningResourceType": "Quiz",
          "educationalUse": "assessment",
          "timeRequired": data.timeRequired ? `PT${data.timeRequired}M` : "PT5M",
          "interactivityType": "active",
          "typicalAgeRange": "16-99",
          "audience": {
            "@type": "EducationalAudience",
            "educationalRole": "student"
          },
          "about": {
            "@type": "Thing",
            "name": "English Language Learning"
          },
          "teaches": data.skills || ["English comprehension", "Grammar", "Vocabulary"],
          "assesses": data.skills || ["English proficiency"],
          "monetization": {
            "@type": "MonetizationModel",
            "name": "advertising-supported"
          },
          "funding": {
            "@type": "Grant",
            "name": "Google AdSense"
          }
        };

      case 'article':
        return {
          ...baseData,
          "@type": "Article",
          "headline": data.title,
          "description": data.description,
          "author": {
            "@type": "Person",
            "name": data.author
          },
          "publisher": {
            "@type": "Organization",
            "name": "Infinite English",
            "logo": {
              "@type": "ImageObject",
              "url": "https://inenglish.io.vn/logo.png"
            }
          },
          "datePublished": data.publishDate,
          "dateModified": data.modifiedDate || data.publishDate,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data.url
          },
          "articleSection": data.category,
          "keywords": data.tags?.join(", "),
          "wordCount": data.wordCount,
          "timeRequired": data.readTime ? `PT${data.readTime}M` : undefined,
          "inLanguage": "vi",
          "isAccessibleForFree": true,
          "monetization": {
            "@type": "MonetizationModel",
            "name": "advertising-supported"
          }
        };

      default:
        return baseData;
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData())
      }}
    />
  );
}

export function VocabularyTopicsStructuredData({ topics, language }: VocabularyTopicsStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": language === 'vi' ? 'Từ Vựng Theo Chủ Đề' :
           language === 'zh' ? '主题词汇' : 'Vocabulary Topics',
    "description": language === 'vi' ? 'Khám phá từ vựng tiếng Anh theo các chủ đề khác nhau' :
                  language === 'zh' ? '按主题探索英语词汇' : 'Explore English vocabulary by topics',
    "url": "https://infiniteenglish.vercel.app/vocabulary",
    "inLanguage": language,
    "isPartOf": {
      "@type": "WebSite",
      "name": "Infinite English",
      "url": "https://infiniteenglish.vercel.app"
    },
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": topics.length,
      "itemListElement": topics.map((topic, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Course",
          "name": topic.name,
          "description": topic.description,
          "url": `https://infiniteenglish.vercel.app/vocabulary/${topic.slug}`,
          "educationalLevel": topic.level,
          "inLanguage": "en",
          "teaches": topic.name,
          "courseCode": topic.slug,
          "numberOfCredits": topic.wordCount,
          "provider": {
            "@type": "Organization",
            "name": "Infinite English",
            "url": "https://infiniteenglish.vercel.app"
          }
        }
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://infiniteenglish.vercel.app"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": language === 'vi' ? 'Từ vựng' : language === 'zh' ? '词汇' : 'Vocabulary',
          "item": "https://infiniteenglish.vercel.app/vocabulary"
        }
      ]
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

export function VocabularyTopicStructuredData({ topic, words, language }: VocabularyTopicStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Course",
    "name": topic.name,
    "description": topic.description,
    "url": `https://infiniteenglish.vercel.app/vocabulary/${topic.slug}`,
    "educationalLevel": topic.level,
    "inLanguage": "en",
    "teaches": topic.name,
    "courseCode": topic.slug,
    "numberOfCredits": topic.wordCount,
    "provider": {
      "@type": "Organization",
      "name": "Infinite English",
      "url": "https://infiniteenglish.vercel.app",
      "logo": {
        "@type": "ImageObject",
        "url": "https://infiniteenglish.vercel.app/logo.png"
      }
    },
    "hasPart": words.slice(0, 10).map(word => ({
      "@type": "DefinedTerm",
      "name": word.word,
      "description": word.definitions[0]?.meaning || '',
      "inDefinedTermSet": {
        "@type": "DefinedTermSet",
        "name": topic.name,
        "description": topic.description
      }
    })),
    "isPartOf": {
      "@type": "WebSite",
      "name": "Infinite English",
      "url": "https://infiniteenglish.vercel.app"
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://infiniteenglish.vercel.app"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": language === 'vi' ? 'Từ vựng' : language === 'zh' ? '词汇' : 'Vocabulary',
          "item": "https://infiniteenglish.vercel.app/vocabulary"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": topic.name,
          "item": `https://infiniteenglish.vercel.app/vocabulary/${topic.slug}`
        }
      ]
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

export function VocabularyLevelStructuredData({ level, topics, language }: { level: string, topics: VocabularyTopic[], language: string }) {
  const levelText = level === 'beginner' ? (language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner') :
                   level === 'intermediate' ? (language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate') :
                   (language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced');

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": language === 'vi' ? `Từ Vựng ${levelText}` :
           language === 'zh' ? `${levelText}词汇` : `${levelText} Vocabulary`,
    "description": language === 'vi' ? `Khám phá từ vựng tiếng Anh cấp độ ${levelText.toLowerCase()}` :
                  language === 'zh' ? `探索${levelText}级英语词汇` : `Explore ${levelText.toLowerCase()}-level English vocabulary`,
    "url": `https://infiniteenglish.vercel.app/vocabulary/${level}`,
    "inLanguage": language,
    "isPartOf": {
      "@type": "WebSite",
      "name": "Infinite English",
      "url": "https://infiniteenglish.vercel.app"
    },
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": topics.length,
      "itemListElement": topics.map((topic, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Course",
          "name": topic.name,
          "description": topic.description,
          "url": `https://infiniteenglish.vercel.app/vocabulary/${topic.slug}`,
          "educationalLevel": topic.level,
          "inLanguage": "en",
          "teaches": topic.name,
          "courseCode": topic.slug,
          "numberOfCredits": topic.wordCount
        }
      }))
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://infiniteenglish.vercel.app"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": language === 'vi' ? 'Từ vựng' : language === 'zh' ? '词汇' : 'Vocabulary',
          "item": "https://infiniteenglish.vercel.app/vocabulary"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": levelText,
          "item": `https://infiniteenglish.vercel.app/vocabulary/${level}`
        }
      ]
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

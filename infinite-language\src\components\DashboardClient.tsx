'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import Link from 'next/link';

interface UserStats {
  totalQuestions: number;
  correctAnswers: number;
  quizzesTaken: number;
  averageAccuracy: number;
  streakCount: number;
  totalStudyTime: number;
  lessonsCompleted: number;
  vocabularyLearned: number;
  levelStats: {
    beginner: { total: number; correct: number };
    intermediate: { total: number; correct: number };
    advanced: { total: number; correct: number };
  };
}

interface Assessment {
  overallPerformance: string;
  strengths: string[];
  weaknesses: string[];
  levelAssessment: string;
  improvementAreas: string[];
  motivationalMessage: string;
}

export default function DashboardClient() {
  const { user } = useAuth();
  const { language, t } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchUserData();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/user/stats');
      const data = await response.json();
      
      if (data.success) {
        setUserStats(data.stats);
        
        // If user has quiz history, get AI assessment
        if (data.stats.quizzesTaken > 0) {
          fetchAssessment();
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAssessment = async () => {
    try {
      // This would typically use recent quiz results
      // For now, we'll use a mock request
      const mockQuizResults = [
        { isCorrect: true, level: 'beginner' },
        { isCorrect: false, level: 'intermediate' },
        { isCorrect: true, level: 'beginner' }
      ];

      const response = await fetch('/api/ai/assessment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quizResults: mockQuizResults,
          language,
          requestType: 'analysis'
        }),
      });

      const data = await response.json();
      if (data.success) {
        setAssessment(data.assessment);
      }
    } catch (error) {
      console.error('Error fetching assessment:', error);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        
        <div className="pt-20 pb-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-12 shadow-xl border border-white/20">
              <div className="text-6xl mb-6">📊</div>
              <h1 className="text-3xl font-bold text-gray-800 mb-4">Learning Dashboard</h1>
              <p className="text-gray-600 mb-8">
                Sign in to track your progress, view detailed analytics, and get personalized learning recommendations.
              </p>
              <button
                onClick={() => setShowAuthModal(true)}
                className="px-8 py-3 gradient-secondary text-white rounded-lg hover:bg-secondary-dark transition-colors font-medium"
              >
                Sign In to View Dashboard
              </button>
            </div>
          </div>
        </div>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    );
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 80) return 'text-secondary-30';
    if (accuracy >= 60) return 'text-highlight-dark';
    return 'text-red-600';
  };

  const getLevelProgress = (level: 'beginner' | 'intermediate' | 'advanced') => {
    if (!userStats?.levelStats[level]) return 0;
    const { total, correct } = userStats.levelStats[level];
    return total > 0 ? Math.round((correct / total) * 100) : 0;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => window.history.back()}
      />

      <div className="pt-20 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl pb-2 font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4">
              {t('dashboardTitle')}
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('dashboardSubtitle')}
            </p>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">📝</span>
                </div>
                <span className="text-2xl font-bold text-gray-800">{userStats?.quizzesTaken || 0}</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">Quizzes Taken</h3>
              <p className="text-gray-600 text-sm">Total practice sessions</p>
            </div>

            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">🎯</span>
                </div>
                <span className={`text-2xl font-bold ${getAccuracyColor(userStats?.averageAccuracy || 0)}`}>
                  {userStats?.averageAccuracy || 0}%
                </span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">Accuracy</h3>
              <p className="text-gray-600 text-sm">Overall performance</p>
            </div>

            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">🔥</span>
                </div>
                <span className="text-2xl font-bold text-gray-800">{userStats?.streakCount || 0}</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">Streak</h3>
              <p className="text-gray-600 text-sm">Days in a row</p>
            </div>

            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <span className="text-white text-xl">⏱️</span>
                </div>
                <span className="text-2xl font-bold text-gray-800">{Math.round((userStats?.totalStudyTime || 0) / 60)}h</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800">Study Time</h3>
              <p className="text-gray-600 text-sm">Total hours practiced</p>
            </div>
          </div>

          {/* Level Progress */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-8 shadow-xl border border-white/20">
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Progress by Level</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {(['beginner', 'intermediate', 'advanced'] as const).map((level) => {
                const progress = getLevelProgress(level);
                const stats = userStats?.levelStats[level];
                
                return (
                  <div key={level} className="text-center">
                    <div className="w-24 h-24 mx-auto mb-4 relative">
                      <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          stroke="currentColor"
                          strokeWidth="8"
                          fill="transparent"
                          className="text-gray-200"
                        />
                        <circle
                          cx="50"
                          cy="50"
                          r="40"
                          stroke="currentColor"
                          strokeWidth="8"
                          fill="transparent"
                          strokeDasharray={`${progress * 2.51} 251`}
                          className={
                            level === 'beginner' ? 'text-green-500' :
                            level === 'intermediate' ? 'text-orange-500' : 'text-red-500'
                          }
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-lg font-bold text-gray-800">{progress}%</span>
                      </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800 capitalize">{level}</h4>
                    <p className="text-gray-600 text-sm">
                      {stats?.correct || 0}/{stats?.total || 0} correct
                    </p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* AI Assessment */}
          {assessment && (
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-8 shadow-xl border border-white/20">
              <h3 className="text-xl font-semibold text-gray-800 mb-6">AI Performance Analysis</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-3">Overall Performance</h4>
                  <p className="text-gray-700 mb-4">{assessment.overallPerformance}</p>
                  
                  <h4 className="text-lg font-semibold text-green-600 mb-3">Strengths</h4>
                  <ul className="space-y-2 mb-4">
                    {assessment.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2">✓</span>
                        <span className="text-gray-700">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-orange-600 mb-3">Areas for Improvement</h4>
                  <ul className="space-y-2 mb-4">
                    {assessment.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-orange-500 mr-2">⚠</span>
                        <span className="text-gray-700">{weakness}</span>
                      </li>
                    ))}
                  </ul>

                  <h4 className="text-lg font-semibold text-blue-600 mb-3">Next Steps</h4>
                  <ul className="space-y-2">
                    {assessment.improvementAreas.map((area, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">→</span>
                        <span className="text-gray-700">{area}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="text-lg font-semibold text-blue-800 mb-2">Motivational Message</h4>
                <p className="text-blue-700">{assessment.motivationalMessage}</p>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Continue Learning</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link
                href="/"
                className="p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-colors text-center"
              >
                <div className="text-2xl mb-2">🎯</div>
                <div className="font-medium">Take Quiz</div>
              </Link>

              <Link
                href="/lessons"
                className="p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-colors text-center"
              >
                <div className="text-2xl mb-2">🎓</div>
                <div className="font-medium">AI Lessons</div>
              </Link>

              <Link
                href="/reading"
                className="p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-colors text-center"
              >
                <div className="text-2xl mb-2">📰</div>
                <div className="font-medium">AI Reading</div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />

      <Footer />
    </div>
  );
}

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_AI_webngonngu_infinite_language_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_AI_webngonngu_infinite_language_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzQztBQUNHO0FBRXpDLE1BQU1FLFVBQVVGLDBEQUFRQSxDQUFDQyxrREFBV0E7QUFFTyIsInNvdXJjZXMiOlsiRDpcXEFJXFx3ZWJuZ29ubmd1XFxpbmZpbml0ZS1sYW5ndWFnZVxcc3JjXFxhcHBcXGFwaVxcYXV0aFxcWy4uLm5leHRhdXRoXVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gXCJuZXh0LWF1dGgvbmV4dFwiO1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tIFwiQC9saWIvYXV0aFwiO1xuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xuXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH07XG4iXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n\n// import FacebookProvider from \"next-auth/providers/facebook\";\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            if (user && account) {\n                // For social login, create/update user in our database\n                try {\n                    await (0,_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                    const existingUser = await _models_User__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n                        email: user.email\n                    });\n                    if (!existingUser) {\n                        // Create new user for social login\n                        const newUser = new _models_User__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n                            email: user.email,\n                            username: user.name || user.email?.split('@')[0],\n                            provider: account.provider,\n                            stats: {\n                                totalQuestions: 0,\n                                correctAnswers: 0,\n                                streakCount: 0,\n                                lastActiveDate: new Date(),\n                                levelStats: {\n                                    beginner: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    intermediate: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    advanced: {\n                                        total: 0,\n                                        correct: 0\n                                    }\n                                }\n                            },\n                            preferredLanguage: 'en'\n                        });\n                        const savedUser = await newUser.save();\n                        token.userId = savedUser._id.toString();\n                        token.username = savedUser.username;\n                    } else {\n                        // Update existing user's last active date\n                        existingUser.stats.lastActiveDate = new Date();\n                        await existingUser.save();\n                        token.userId = existingUser._id.toString();\n                        token.username = existingUser.username;\n                    }\n                } catch (error) {\n                    console.error(\"Error creating/updating user:\", error);\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.userId;\n                session.user.username = token.username;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n// Global is used here to maintain a cached connection across hot reloads in development\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null\n};\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    username: {\n        type: String,\n        required: true,\n        unique: true,\n        trim: true,\n        minlength: 3,\n        maxlength: 20\n    },\n    password: {\n        type: String,\n        required: false,\n        minlength: 6\n    },\n    provider: {\n        type: String,\n        enum: [\n            'google',\n            'facebook',\n            'credentials'\n        ],\n        default: 'credentials'\n    },\n    preferredLanguage: {\n        type: String,\n        enum: [\n            'en',\n            'vi',\n            'zh'\n        ],\n        default: 'vi'\n    },\n    stats: {\n        totalQuestions: {\n            type: Number,\n            default: 0\n        },\n        correctAnswers: {\n            type: Number,\n            default: 0\n        },\n        streakCount: {\n            type: Number,\n            default: 0\n        },\n        lastActiveDate: {\n            type: Date,\n            default: Date.now\n        },\n        quizzesTaken: {\n            type: Number,\n            default: 0\n        },\n        averageAccuracy: {\n            type: Number,\n            default: 0\n        },\n        lastQuizDate: {\n            type: Date\n        },\n        totalStudyTime: {\n            type: Number,\n            default: 0\n        },\n        lessonsCompleted: {\n            type: Number,\n            default: 0\n        },\n        vocabularyLearned: {\n            type: Number,\n            default: 0\n        },\n        levelStats: {\n            beginner: {\n                total: {\n                    type: Number,\n                    default: 0\n                },\n                correct: {\n                    type: Number,\n                    default: 0\n                }\n            },\n            intermediate: {\n                total: {\n                    type: Number,\n                    default: 0\n                },\n                correct: {\n                    type: Number,\n                    default: 0\n                }\n            },\n            advanced: {\n                total: {\n                    type: Number,\n                    default: 0\n                },\n                correct: {\n                    type: Number,\n                    default: 0\n                }\n            }\n        }\n    }\n}, {\n    timestamps: true\n});\n// Hash password before saving\nUserSchema.pre('save', async function() {\n    if (!this.isModified('password') || !this.password) return;\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(this.password, salt);\n    } catch (error) {\n        throw error;\n    }\n});\n// Compare password method (only for users with password)\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n    if (!this.password) {\n        return false; // Social login users don't have passwords\n    }\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(candidatePassword, this.password);\n};\n// Prevent password from being returned in JSON\nUserSchema.methods.toJSON = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/@panva","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
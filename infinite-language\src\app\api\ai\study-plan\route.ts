import { NextRequest, NextResponse } from 'next/server';
import { generateStudyPlanWithGemini } from '@/lib/gemini';
import { Level, Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { 
      level, 
      language = 'en', 
      goals, 
      timeAvailable, 
      weakAreas,
      preferredLearningStyle 
    } = await request.json();

    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    if (!timeAvailable || timeAvailable < 1) {
      return NextResponse.json(
        { success: false, error: 'Time available must be at least 1 hour per week' },
        { status: 400 }
      );
    }

    // Generate personalized study plan using Gemini AI
    const studyPlan = await generateStudyPlanWithGemini({
      level,
      language,
      goals: goals || [],
      timeAvailable,
      weakAreas: weakAreas || [],
      preferredLearningStyle: preferredLearningStyle || 'mixed'
    });

    return NextResponse.json({
      success: true,
      studyPlan
    });
  } catch (error) {
    console.error('Error generating study plan:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate study plan' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { generateReadingPassageWithGemini } from '@/lib/gemini';
import { Level, Language } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { 
      level, 
      language = 'en', 
      topic,
      wordCount = 300,
      passageType = 'general'
    } = await request.json();

    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    const validTypes = ['general', 'news', 'story', 'academic', 'science', 'history', 'culture'];
    if (!validTypes.includes(passageType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid passage type provided' },
        { status: 400 }
      );
    }

    if (wordCount < 100 || wordCount > 1000) {
      return NextResponse.json(
        { success: false, error: 'Word count must be between 100 and 1000' },
        { status: 400 }
      );
    }

    // Generate reading passage using Gemini AI
    const readingPassage = await generateReadingPassageWithGemini({
      level,
      language,
      topic,
      wordCount,
      passageType
    });

    return NextResponse.json({
      success: true,
      passage: readingPassage
    });
  } catch (error) {
    console.error('Error generating reading passage:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate reading passage' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { VocabularyTopic, VocabularyWord } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import LoadingSpinner from '@/components/LoadingSpinner';
import VocabularyWordCard from '@/components/VocabularyWordCard';
import AdSense from '@/components/AdSense';
import { VocabularyTopicStructuredData } from '@/components/StructuredData';

interface Props {
  slug: string;
  initialTopic: VocabularyTopic;
  initialWords: VocabularyWord[];
}

export default function VocabularyTopicServerPage({
  slug,
  initialTopic,
  initialWords
}: Props) {
  const router = useRouter();
  const { language } = useLanguage();
  const [topic, setTopic] = useState<VocabularyTopic>(initialTopic);
  const [words, setWords] = useState<VocabularyWord[]>(initialWords);
  const [loading, setLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Note: Language changes are handled by the language context
  // which will trigger a page refresh automatically

  // Update data when search term or page changes - use API for dynamic content
  useEffect(() => {
    if (searchTerm || currentPage > 1) {
      fetchWords();
    }
  }, [searchTerm, currentPage]);

  const fetchWords = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        topic: slug,
        language,
        page: currentPage.toString(),
        limit: '20',
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const wordsResponse = await fetch(`/api/vocabulary/words?${params}`);
      const wordsData = await wordsResponse.json();

      if (wordsData.success) {
        setWords(wordsData.data.words);
        setTotalPages(wordsData.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching words:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const getLevelText = (level: string) => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getBreadcrumbText = () => {
    switch (language) {
      case 'vi':
        return 'Từ vựng';
      case 'zh':
        return '词汇';
      default:
        return 'Vocabulary';
    }
  };

  const getSearchPlaceholder = () => {
    switch (language) {
      case 'vi':
        return 'Tìm kiếm từ vựng...';
      case 'zh':
        return '搜索词汇...';
      default:
        return 'Search vocabulary...';
    }
  };

  if (loading && words.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
        <div className="pt-24">
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <>
      <VocabularyTopicStructuredData topic={topic} words={words} language={language} />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <ol className="flex items-center space-x-2 text-sm text-gray-600">
              <li>
                <button
                  onClick={() => router.push('/vocabulary')}
                  className="hover:text-blue-600 transition-colors duration-200"
                >
                  {getBreadcrumbText()}
                </button>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-gray-900 font-medium">{topic.name}</li>
            </ol>
          </nav>

          {/* Topic Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-4">
              <div 
                className="w-16 h-16 rounded-xl flex items-center justify-center text-3xl mr-4"
                style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
              >
                {topic.icon}
              </div>
              <div className="text-left">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                  {topic.name}
                </h1>
                <div className="flex items-center space-x-3">
                  <span className={`text-sm px-3 py-1 rounded-full ${
                    topic.level === 'beginner' ? 'bg-green-100 text-green-700' :
                    topic.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {getLevelText(topic.level)}
                  </span>
                  <span className="text-sm text-gray-500">
                    {topic.wordCount} {language === 'vi' ? 'từ' : language === 'zh' ? '词' : 'words'}
                  </span>
                </div>
              </div>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {topic.description}
            </p>
          </div>

          {/* Search Bar */}
          <div className="flex justify-center mb-8">
            <div className="w-full max-w-md">
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder={getSearchPlaceholder()}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
              />
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense adSlot="vocabulary-topic" />
          </div>

          {/* Loading overlay */}
          {loading && (
            <div className="relative">
              <div className="absolute inset-0 bg-white bg-opacity-75 z-10 flex items-center justify-center">
                <LoadingSpinner />
              </div>
            </div>
          )}

          {/* Words Grid */}
          {words.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {words.map((word) => (
                  <VocabularyWordCard key={word._id} word={word} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center">
                  <div className="flex space-x-2">
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                          currentPage === page
                            ? 'bg-blue-500 text-white shadow-md'
                            : 'bg-white text-gray-600 hover:bg-gray-100 shadow-sm'
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {searchTerm 
                  ? (language === 'vi' ? 'Không tìm thấy từ nào' : 
                     language === 'zh' ? '未找到任何单词' : 
                     'No words found')
                  : (language === 'vi' ? 'Chưa có từ vựng nào' : 
                     language === 'zh' ? '暂无词汇' : 
                     'No vocabulary yet')
                }
              </h3>
              <p className="text-gray-500">
                {searchTerm 
                  ? (language === 'vi' ? 'Thử tìm kiếm với từ khóa khác.' : 
                     language === 'zh' ? '尝试使用其他关键词搜索。' : 
                     'Try searching with different keywords.')
                  : (language === 'vi' ? 'Chúng tôi đang cập nhật từ vựng cho chủ đề này.' : 
                     language === 'zh' ? '我们正在为此主题更新词汇。' : 
                     'We are updating vocabulary for this topic.')
                }
              </p>
            </div>
          )}
        </div>
      </div>

      <Footer />
      
        {showAuthModal && (
          <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
        )}
      </div>
    </>
  );
}

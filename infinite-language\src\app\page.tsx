'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Level, QuizState } from '@/types';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import LevelSelector from '@/components/LevelSelector';
import QuestionCard from '@/components/QuestionCard';
import ResultCard from '@/components/ResultCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import AuthModal from '@/components/AuthModal';
import HistoryPage from '@/components/HistoryPage';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function Home() {
  const router = useRouter();
  const { language } = useLanguage();
  const { user } = useAuth();
  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestion: null,
    selectedAnswer: null,
    showResult: false,
    score: { correct: 0, total: 0 },
    level: null,
    language: 'en'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState<number>(0);

  const generateQuestion = async (level: Level) => {
    // Clear current question immediately to prevent showing old content
    setQuizState(prev => ({
      ...prev,
      currentQuestion: null,
      selectedAnswer: null,
      showResult: false
    }));

    setIsLoading(true);
    try {
      const response = await fetch('/api/generate-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ level, language }),
      });

      const data = await response.json();

      if (data.success && data.question) {
        setQuizState(prev => ({
          ...prev,
          currentQuestion: data.question,
          selectedAnswer: null,
          showResult: false,
          level,
          language
        }));
        setQuestionStartTime(Date.now());
      } else {
        console.error('Failed to generate question:', data.error);
      }
    } catch (error) {
      console.error('Error fetching question:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLevelSelect = (level: Level) => {
    // Navigate to SSR quiz page using Next.js router
    router.push(`/quiz/${level}?lang=${language}`);
  };

  const handleAnswerSelect = async (answerIndex: number) => {
    if (quizState.showResult || !quizState.currentQuestion) return;

    setQuizState(prev => ({
      ...prev,
      selectedAnswer: answerIndex
    }));

    // Submit answer to server for validation
    try {
      const response = await fetch('/api/submit-answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId: quizState.currentQuestion.id,
          userAnswer: answerIndex
        }),
      });

      const data = await response.json();

      if (data.success && data.result) {
        const { isCorrect, correctAnswer, explanation } = data.result;
        const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

        // Update quiz state with result
        setQuizState(prev => ({
          ...prev,
          showResult: true,
          currentQuestion: prev.currentQuestion ? {
            ...prev.currentQuestion,
            correctAnswer,
            explanation
          } : null,
          score: {
            correct: prev.score.correct + (isCorrect ? 1 : 0),
            total: prev.score.total + 1
          }
        }));

        // Save to history if user is logged in
        if (user && quizState.currentQuestion) {
          try {
            await fetch('/api/quiz/history', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                questionId: quizState.currentQuestion.id,
                question: quizState.currentQuestion.question,
                options: quizState.currentQuestion.options,
                correctAnswer: correctAnswer,
                userAnswer: answerIndex,
                explanation: explanation,
                level: quizState.currentQuestion.level,
                language: language,
                timeSpent: timeSpent,
              }),
            });
          } catch (error) {
            console.error('Failed to save quiz history:', error);
          }
        }
      } else {
        console.error('Failed to submit answer:', data.error);
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
    }
  };

  const handleNextQuestion = () => {
    if (quizState.level) {
      generateQuestion(quizState.level);
    }
  };

  const handleBackToLevels = () => {
    setQuizState({
      currentQuestion: null,
      selectedAnswer: null,
      showResult: false,
      score: { correct: 0, total: 0 },
      level: null,
      language
    });
  };

  if (showHistory) {
    return <HistoryPage onBack={() => setShowHistory(false)} />;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!quizState.level) {
    return (
      <div className="min-h-screen">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          onHistoryClick={() => setShowHistory(true)}
        />
        <div className="">
          <LevelSelector
            onLevelSelect={handleLevelSelect}
            onAuthClick={() => setShowAuthModal(true)}
            onHistoryClick={() => setShowHistory(true)}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion && quizState.showResult) {
    const isCorrect = quizState.selectedAnswer === quizState.currentQuestion.correctAnswer;
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          onHistoryClick={() => setShowHistory(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={quizState.score}
        />
        <div className="pt-24">
          <ResultCard
            question={quizState.currentQuestion}
            selectedAnswer={quizState.selectedAnswer}
            isCorrect={isCorrect}
            onNextQuestion={handleNextQuestion}
            score={quizState.score}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  if (quizState.currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          onHistoryClick={() => setShowHistory(true)}
          showBackButton={true}
          onBackClick={handleBackToLevels}
          showScore={true}
          score={quizState.score}
        />
        <div className="pt-24">
          <QuestionCard
            question={quizState.currentQuestion}
            selectedAnswer={quizState.selectedAnswer}
            onAnswerSelect={handleAnswerSelect}
            showResult={quizState.showResult}
          />
        </div>
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header
        onAuthClick={() => setShowAuthModal(true)}
        onHistoryClick={() => setShowHistory(true)}
      />
      <div className="pt-24">
        <LevelSelector
          onLevelSelect={handleLevelSelect}
          onAuthClick={() => setShowAuthModal(true)}
          onHistoryClick={() => setShowHistory(true)}
        />
      </div>
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      <Footer />
    </div>
  );
}

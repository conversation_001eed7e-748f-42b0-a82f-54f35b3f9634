import { Metadata } from 'next';
import { headers } from 'next/headers';
import VocabularySimple from '@/components/VocabularySimple';
import { VocabularyTopic } from '@/types';
import connectDB from '@/lib/mongodb';
import VocabularyTopicModel from '@/models/VocabularyTopic';

export const metadata: Metadata = {
  title: 'Vocabulary Topics - Infinite English | Learn English Words by Categories',
  description: 'Explore English vocabulary by topics with over 1000+ words. Learn new words with definitions, examples, pronunciation guides, and interactive exercises for beginner, intermediate, and advanced levels.',
  keywords: 'English vocabulary, vocabulary topics, learn English words, vocabulary practice, English learning, word meanings, vocabulary exercises, English words by category, vocabulary building, language learning',
  authors: [{ name: 'Infinite English' }],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Vocabulary Topics - Infinite English',
    description: 'Explore English vocabulary by topics with interactive learning tools and comprehensive word lists.',
    type: 'website',
    locale: 'vi_VN',
    alternateLocale: ['en_US', 'zh_CN'],
    siteName: 'Infinite English',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vocabulary Topics - Infinite English',
    description: 'Learn English vocabulary by topics with interactive exercises.',
  },
  alternates: {
    canonical: '/vocabulary',
    languages: {
      'vi': '/vocabulary',
      'en': '/vocabulary',
      'zh': '/vocabulary',
    },
  },
};

async function getVocabularyTopics(language: string = 'vi'): Promise<VocabularyTopic[]> {
  try {
    await connectDB();

    const topics = await VocabularyTopicModel
      .find({
        language,
        isActive: true
      })
      .sort({ order: 1, createdAt: -1 })
      .limit(50)
      .lean();

    return topics.map(topic => ({
      ...topic,
      _id: topic._id.toString(),
      createdAt: topic.createdAt.toISOString(),
      updatedAt: topic.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching vocabulary topics:', error);
    return [];
  }
}

export default async function VocabularyPage() {
  // Get language from headers or default to Vietnamese
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const language = acceptLanguage.includes('zh') ? 'zh' :
                  acceptLanguage.includes('en') ? 'en' : 'vi';

  const topics = await getVocabularyTopics(language);

  return <VocabularySimple initialTopics={topics} />;
}

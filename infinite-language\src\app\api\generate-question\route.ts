import { NextRequest, NextResponse } from 'next/server';
import { Level, Language } from '@/types';
import { generateQuestionWithGemini } from '@/lib/gemini';
import { questionStore } from '@/lib/questionStore';



export async function POST(request: NextRequest) {
  try {
    const { level, language = 'en' } = await request.json();

    if (!level || !['beginner', 'intermediate', 'advanced'].includes(level)) {
      return NextResponse.json(
        { success: false, error: 'Invalid level provided' },
        { status: 400 }
      );
    }

    if (!language || !['en', 'vi', 'zh'].includes(language)) {
      return NextResponse.json(
        { success: false, error: 'Invalid language provided' },
        { status: 400 }
      );
    }

    // Generate question using Gemini AI
    const fullQuestion = await generateQuestionWithGemini(level as Level, language as Language);

    // Store the answer and explanation server-side
    await questionStore.set(fullQuestion.id, {
      correctAnswer: fullQuestion.correctAnswer,
      explanation: fullQuestion.explanation,
      level: fullQuestion.level,
      language: language as Language
    });

    // Return only question data without answer
    const safeQuestion = {
      id: fullQuestion.id,
      question: fullQuestion.question,
      options: fullQuestion.options,
      level: fullQuestion.level
    };

    return NextResponse.json({
      success: true,
      question: safeQuestion
    });

  } catch (error) {
    console.error('Error generating question:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate question' },
      { status: 500 }
    );
  }
}



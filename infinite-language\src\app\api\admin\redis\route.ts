import { NextRequest, NextResponse } from 'next/server';
import { questionStore } from '@/lib/questionStore';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        const stats = await questionStore.getStats();
        return NextResponse.json({
          success: true,
          stats: {
            ...stats,
            oldestQuestionAge: stats.oldestQuestion 
              ? Math.floor((Date.now() - stats.oldestQuestion) / 1000 / 60) // minutes
              : null
          }
        });

      case 'cleanup':
        const deletedCount = await questionStore.cleanup();
        return NextResponse.json({
          success: true,
          message: `Cleaned up ${deletedCount} expired questions`
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use ?action=stats or ?action=cleanup'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Redis admin error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { questionId } = await request.json();
    
    if (!questionId) {
      return NextResponse.json(
        { success: false, error: 'Question ID is required' },
        { status: 400 }
      );
    }

    await questionStore.delete(questionId);
    
    return NextResponse.json({
      success: true,
      message: `Question ${questionId} deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete question' },
      { status: 500 }
    );
  }
}

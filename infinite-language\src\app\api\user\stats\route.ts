import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectDB();
    
    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Return user stats
    const stats = {
      totalQuestions: user.stats?.totalQuestions || 0,
      correctAnswers: user.stats?.correctAnswers || 0,
      quizzesTaken: user.stats?.quizzesTaken || 0,
      averageAccuracy: user.stats?.averageAccuracy || 0,
      streakCount: user.stats?.streakCount || 0,
      totalStudyTime: user.stats?.totalStudyTime || 0,
      lessonsCompleted: user.stats?.lessonsCompleted || 0,
      vocabularyLearned: user.stats?.vocabularyLearned || 0,
      levelStats: user.stats?.levelStats || {
        beginner: { total: 0, correct: 0 },
        intermediate: { total: 0, correct: 0 },
        advanced: { total: 0, correct: 0 }
      },
      lastQuizDate: user.stats?.lastQuizDate,
      level: user.level,
      language: user.language
    };

    return NextResponse.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user stats' },
      { status: 500 }
    );
  }
}

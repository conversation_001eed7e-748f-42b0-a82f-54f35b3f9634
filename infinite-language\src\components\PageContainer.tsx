'use client';

import { ReactNode } from 'react';

interface PageContainerProps {
  children: ReactNode;
  className?: string;
  withHeaderSpacing?: boolean;
}

export default function PageContainer({ 
  children, 
  className = '', 
  withHeaderSpacing = true 
}: PageContainerProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 ${className}`}>
      <div className={withHeaderSpacing ? 'pt-24' : ''}>
        {children}
      </div>
    </div>
  );
}

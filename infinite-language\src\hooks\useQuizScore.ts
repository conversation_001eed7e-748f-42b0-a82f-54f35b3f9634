'use client';

import { useState, useEffect } from 'react';
import { Level } from '@/types';

interface QuizScore {
  correct: number;
  total: number;
}

interface QuizSession {
  level: Level;
  score: QuizScore;
  timestamp: number;
}

export function useQuizScore(level: Level) {
  const [score, setScore] = useState<QuizScore>({ correct: 0, total: 0 });

  // Load score from localStorage on mount
  useEffect(() => {
    const savedSession = localStorage.getItem(`quiz-session-${level}`);
    if (savedSession) {
      try {
        const session: QuizSession = JSON.parse(savedSession);
        // Check if session is from today (reset daily)
        const today = new Date().toDateString();
        const sessionDate = new Date(session.timestamp).toDateString();
        
        if (today === sessionDate && session.level === level) {
          setScore(session.score);
        } else {
          // Clear old session
          localStorage.removeItem(`quiz-session-${level}`);
        }
      } catch (error) {
        console.error('Error loading quiz session:', error);
        localStorage.removeItem(`quiz-session-${level}`);
      }
    }
  }, [level]);

  // Save score to localStorage whenever it changes
  useEffect(() => {
    if (score.total > 0) {
      const session: QuizSession = {
        level,
        score,
        timestamp: Date.now()
      };
      localStorage.setItem(`quiz-session-${level}`, JSON.stringify(session));
    }
  }, [score, level]);

  const updateScore = (isCorrect: boolean) => {
    setScore(prev => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1
    }));
  };

  const resetScore = () => {
    setScore({ correct: 0, total: 0 });
    localStorage.removeItem(`quiz-session-${level}`);
  };

  return {
    score,
    updateScore,
    resetScore
  };
}

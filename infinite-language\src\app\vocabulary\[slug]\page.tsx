import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { headers } from 'next/headers';
import VocabularyTopicServerPage from '@/components/VocabularyTopicServerPage';
import { VocabularyTopic, VocabularyWord } from '@/types';
import connectDB from '@/lib/mongodb';
import VocabularyTopicModel from '@/models/VocabularyTopic';
import VocabularyWordModel from '@/models/VocabularyWord';

interface Props {
  params: { slug: string };
}

async function getTopicData(slug: string, language: string = 'vi') {
  try {
    await connectDB();

    // Get topic
    const topic = await VocabularyTopicModel
      .findOne({ slug, language, isActive: true })
      .lean();

    if (!topic) {
      return null;
    }

    // Get words for this topic
    const words = await VocabularyWordModel
      .find({
        topicId: topic._id,
        isActive: true
      })
      .sort({ order: 1, createdAt: -1 })
      .limit(20)
      .lean();

    return {
      topic: {
        ...topic,
        _id: topic._id.toString(),
        createdAt: topic.createdAt.toISOString(),
        updatedAt: topic.updatedAt.toISOString(),
      },
      words: words.map(word => ({
        ...word,
        _id: word._id.toString(),
        topicId: word.topicId.toString(),
        createdAt: word.createdAt.toISOString(),
        updatedAt: word.updatedAt.toISOString(),
      }))
    };
  } catch (error) {
    console.error('Error fetching topic data:', error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const headersList = await headers();
    const acceptLanguage = headersList.get('accept-language') || '';
    const language = acceptLanguage.includes('zh') ? 'zh' :
                    acceptLanguage.includes('en') ? 'en' : 'vi';

    const data = await getTopicData(params.slug, language);
    const topic = data?.topic;

    if (!topic) {
      return {
        title: 'Topic Not Found - Infinite English',
        description: 'The vocabulary topic you are looking for could not be found.',
      };
    }

    return {
      title: topic.seoTitle || `${topic.name} Vocabulary - Infinite English`,
      description: topic.seoDescription || `Learn ${topic.name.toLowerCase()} vocabulary with definitions, examples, and pronunciation. ${topic.description}`,
      keywords: topic.seoKeywords?.join(', ') || `${topic.name}, English vocabulary, ${topic.level} level, vocabulary practice`,
      openGraph: {
        title: topic.seoTitle || `${topic.name} Vocabulary - Infinite English`,
        description: topic.seoDescription || topic.description,
        type: 'website',
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Vocabulary Topic - Infinite English',
      description: 'Learn English vocabulary with interactive exercises and examples.',
    };
  }
}

export default async function VocabularyTopicPage({ params }: Props) {
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  const language = acceptLanguage.includes('zh') ? 'zh' :
                  acceptLanguage.includes('en') ? 'en' : 'vi';

  const data = await getTopicData(params.slug, language);

  if (!data) {
    notFound();
  }

  return (
    <VocabularyTopicServerPage
      slug={params.slug}
      initialTopic={data.topic}
      initialWords={data.words}
    />
  );
}

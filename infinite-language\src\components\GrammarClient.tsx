'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import AdSense from '@/components/AdSense';

interface GrammarTopic {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  slug: string;
}

export default function GrammarClient() {
  const router = useRouter();
  const { language } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('all');

  const grammarTopics: GrammarTopic[] = [
    {
      id: '1',
      title: 'Present Tenses',
      description: 'Learn present simple, present continuous, present perfect, and present perfect continuous',
      icon: '⏰',
      color: '#3B82F6',
      level: 'beginner',
      slug: 'present-tenses'
    },
    {
      id: '2',
      title: 'Past Tenses',
      description: 'Master past simple, past continuous, past perfect, and past perfect continuous',
      icon: '📅',
      color: '#10B981',
      level: 'beginner',
      slug: 'past-tenses'
    },
    {
      id: '3',
      title: 'Future Tenses',
      description: 'Understand future simple, future continuous, future perfect, and going to',
      icon: '🔮',
      color: '#F59E0B',
      level: 'intermediate',
      slug: 'future-tenses'
    },
    {
      id: '4',
      title: 'Modal Verbs',
      description: 'Learn can, could, may, might, must, should, will, would and their uses',
      icon: '🎭',
      color: '#8B5CF6',
      level: 'intermediate',
      slug: 'modal-verbs'
    },
    {
      id: '5',
      title: 'Passive Voice',
      description: 'Transform active sentences to passive and understand when to use passive voice',
      icon: '🔄',
      color: '#EF4444',
      level: 'intermediate',
      slug: 'passive-voice'
    },
    {
      id: '6',
      title: 'Conditionals',
      description: 'Master zero, first, second, third, and mixed conditionals',
      icon: '🤔',
      color: '#06B6D4',
      level: 'advanced',
      slug: 'conditionals'
    },
    {
      id: '7',
      title: 'Reported Speech',
      description: 'Learn how to report what someone said using indirect speech',
      icon: '💬',
      color: '#F97316',
      level: 'advanced',
      slug: 'reported-speech'
    },
    {
      id: '8',
      title: 'Articles',
      description: 'Understand when to use a, an, the, or no article',
      icon: '📝',
      color: '#84CC16',
      level: 'beginner',
      slug: 'articles'
    },
    {
      id: '9',
      title: 'Prepositions',
      description: 'Learn prepositions of time, place, and movement',
      icon: '📍',
      color: '#EC4899',
      level: 'intermediate',
      slug: 'prepositions'
    },
    {
      id: '10',
      title: 'Phrasal Verbs',
      description: 'Master common phrasal verbs and their meanings',
      icon: '🔗',
      color: '#6366F1',
      level: 'advanced',
      slug: 'phrasal-verbs'
    }
  ];

  const filteredTopics = selectedLevel === 'all' 
    ? grammarTopics 
    : grammarTopics.filter(topic => topic.level === selectedLevel);

  const getLevelText = (level: string) => {
    switch (level) {
      case 'beginner':
        return language === 'vi' ? 'Cơ bản' : language === 'zh' ? '初级' : 'Beginner';
      case 'intermediate':
        return language === 'vi' ? 'Trung cấp' : language === 'zh' ? '中级' : 'Intermediate';
      case 'advanced':
        return language === 'vi' ? 'Nâng cao' : language === 'zh' ? '高级' : 'Advanced';
      default:
        return level;
    }
  };

  const getPageTitle = () => {
    switch (language) {
      case 'vi':
        return 'Ngữ Pháp Tiếng Anh';
      case 'zh':
        return '英语语法';
      default:
        return 'English Grammar';
    }
  };

  const getPageDescription = () => {
    switch (language) {
      case 'vi':
        return 'Học ngữ pháp tiếng Anh từ cơ bản đến nâng cao với các bài học chi tiết và bài tập thực hành.';
      case 'zh':
        return '从基础到高级学习英语语法，包含详细课程和练习。';
      default:
        return 'Master English grammar from basic to advanced with detailed lessons and practice exercises.';
    }
  };

  const handleTopicClick = (slug: string) => {
    router.push(`/grammar/${slug}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {getPageTitle()}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
          </div>

          {/* Level Filter */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-xl p-2 shadow-lg">
              <div className="flex space-x-2">
                {(['all', 'beginner', 'intermediate', 'advanced'] as const).map((level) => (
                  <button
                    key={level}
                    onClick={() => setSelectedLevel(level)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedLevel === level
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {level === 'all' 
                      ? (language === 'vi' ? 'Tất cả' : language === 'zh' ? '全部' : 'All')
                      : getLevelText(level)
                    }
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense />
          </div>

          {/* Grammar Topics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredTopics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => handleTopicClick(topic.slug)}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
              >
                <div className="flex items-center mb-4">
                  <div 
                    className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl mr-4"
                    style={{ backgroundColor: `${topic.color}20`, color: topic.color }}
                  >
                    {topic.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-bold text-lg text-gray-900 mb-1">
                      {topic.title}
                    </h3>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {getLevelText(topic.level)}
                    </span>
                  </div>
                </div>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {topic.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {language === 'vi' ? 'Bài học' : language === 'zh' ? '课程' : 'Lesson'}
                  </span>
                  <div className="text-blue-500 text-sm font-medium">
                    {language === 'vi' ? 'Học ngay →' : language === 'zh' ? '立即学习 →' : 'Learn now →'}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Practice Section */}
          <div className="mt-12 text-center bg-white rounded-xl p-8 shadow-lg">
            <div className="text-4xl mb-4">🎯</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {language === 'vi' ? 'Luyện tập ngữ pháp' : language === 'zh' ? '语法练习' : 'Practice Grammar'}
            </h3>
            <p className="text-gray-600">
              {language === 'vi'
                ? 'Luyện tập ngữ pháp tiếng Anh với các câu hỏi quiz đa dạng và thú vị.'
                : language === 'zh'
                ? '通过多样化和有趣的测验问题练习英语语法。'
                : 'Practice English grammar with diverse and engaging quiz questions.'
              }
            </p>
            <button
              onClick={() => router.push('/')}
              className="mt-4 bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              {language === 'vi' ? 'Làm Quiz ngay' : language === 'zh' ? '立即做测验' : 'Take Quiz Now'}
            </button>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      
      <Footer />
    </div>
  );
}
